package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;

import java.util.Map;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
public interface ActivityImageService extends IService<ActivityImageEntity> {

    PageUtils queryPage(Map<String, Object> params);

    List<ActivityImageEntity> findByIds(List<Long> ids);
    List<ActivityImageEntity> findByActivityIdNoUseLimit(Long activityId,Integer limit);
    void incrementUseCount(Long imageId);

    // 新增按平台的方法
    List<ActivityImageEntity> findByActivityIdNoUseLimitByPlatform(Long activityId, String platform, Integer limit);
    void incrementUseCountByPlatform(Long imageId, String platform, Long activityId);
    Integer getUsageCountByPlatform(Long imageId, String platform);
}

