package com.cjy.pyp.modules.activity.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import com.cjy.pyp.common.constant.RedisScriptConstant;
import com.cjy.pyp.common.constant.TokenConstant;
import com.cjy.pyp.common.exception.RRException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import com.cjy.pyp.modules.activity.dto.JobStatusResponse;
import com.cjy.pyp.modules.activity.dto.VideoEditRequest;
import com.cjy.pyp.modules.activity.dto.VideoEditResponse;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;
import com.cjy.pyp.modules.activity.service.ActivityVideoService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.PlatformMediaConfigService;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.activity.service.ActivityVideoConnectService;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.entity.ActivityVideoConnectEntity;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.common.utils.PageUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.common.utils.R;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
@RestController
@RequestMapping("activity/activityvideo")
public class ActivityVideoController extends AbstractController {
    @Autowired
    private ActivityVideoService activityVideoService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;
    @Autowired
    private PlatformMediaConfigService platformMediaConfigService;
    @Autowired
    private ActivityImageService activityImageService;
    @Autowired
    private ActivityTextService activityTextService;
    @Autowired
    private ActivityVideoConnectService activityVideoConnectService;


    @PostMapping("/uploadVideo")
    public R uploadAlyVideo(@RequestParam("file") MultipartFile file,
                            @RequestParam("activityId") Long activityId,
                            @CookieValue String appid) {
        //返回上传视频id
        String videoId = activityVideoService.uploadVideoAly(file,activityId,appid);
        return R.ok().put("videoId", videoId);
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:activityvideo:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = activityVideoService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:activityvideo:info")
    public R info(@PathVariable("id") Long id){
		ActivityVideoEntity activityVideo = activityVideoService.getById(id);

        return R.ok().put("activityVideo", activityVideo);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("activity:activityvideo:save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody ActivityVideoEntity activityVideo){
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), activityVideo.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }
		activityVideoService.save(activityVideo);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("activity:activityvideo:update")
    @Transactional(rollbackFor = Exception.class)
    public R update(@RequestBody ActivityVideoEntity activityVideo){
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), activityVideo.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }
		activityVideoService.updateById(activityVideo);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("activity:activityvideo:delete")
    public R delete(@RequestBody Long[] ids){
		activityVideoService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
     * 提交视频混剪任务（自动获取活动下的素材视频和文案）
     */
    @RequestMapping("/submitVideoEdit")
    public R submitVideoEdit(
        @RequestParam("activityId") Long activityId,
        @RequestParam(value = "platform", required = false, defaultValue = "douyin") String platform,
         @CookieValue String appid) {
            VideoEditRequest request = new VideoEditRequest();
            request.setActivityId(activityId);
            request.setPlatform(platform);
            request.setAppid(appid);

            VideoEditResponse response = activityVideoService.submitVideoEdit(request, getUserId());
            return R.ok().put("response", response);
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/jobStatus/{jobId}")
    public R getJobStatus(@PathVariable("jobId") String jobId, @CookieValue String appid) {
        try {
            JobStatusResponse response = activityVideoService.getJobStatus(jobId, appid);
            return R.ok().put("response", response);
        } catch (Exception e) {
            return R.error("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 轮询任务状态（专用于前端轮询）
     */
    @GetMapping("/pollJobStatus/{jobId}")
    @RequiresPermissions("activity:activityvideo:info")
    public R pollJobStatus(@PathVariable("jobId") String jobId, @CookieValue String appid) {
        try {
            JobStatusResponse response = activityVideoService.getJobStatus(jobId, appid);

            // 构建轮询专用的响应
            Map<String, Object> pollResponse = new HashMap<>();
            pollResponse.put("jobId", response.getJobId());
            pollResponse.put("status", response.getStatus());
            pollResponse.put("progress", response.getProgress());
            pollResponse.put("outputUrl", response.getOutputUrl());
            pollResponse.put("errorMessage", response.getErrorMessage());

            // 添加轮询控制信息
            boolean isCompleted = "Success".equals(response.getStatus()) || "Failed".equals(response.getStatus());
            pollResponse.put("isCompleted", isCompleted);
            pollResponse.put("shouldContinuePolling", !isCompleted);

            // 根据状态返回不同的提示信息
            String statusMessage = getStatusMessage(response.getStatus());
            pollResponse.put("statusMessage", statusMessage);

            return R.ok().put("data", pollResponse);
        } catch (Exception e) {
            return R.error("轮询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 生成字幕
     */
    @PostMapping("/generateSubtitle")
    public R generateSubtitle(@RequestParam("videoId") Long videoId, @CookieValue String appid) {
        try {
            String jobId = activityVideoService.generateSubtitle(videoId, appid);
            return R.ok().put("jobId", jobId);
        } catch (Exception e) {
            return R.error("生成字幕失败: " + e.getMessage());
        }
    }

    /**
     * 语音合成
     */
    @PostMapping("/synthesizeVoice")
    public R synthesizeVoice(@RequestParam("text") String text,
                           @RequestParam(value = "voice", required = false) String voice,
                           @CookieValue String appid) {
        try {
            String jobId = activityVideoService.synthesizeVoice(text, voice, appid);
            return R.ok().put("jobId", jobId);
        } catch (Exception e) {
            return R.error("语音合成失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动下的所有混剪任务
     */
    @GetMapping("/editJobs/{activityId}")
    @RequiresPermissions("activity:activityvideo:info")
    public R getEditJobs(@PathVariable("activityId") Long activityId, @CookieValue String appid) {
        try {
            List<JobStatusResponse> jobs = activityVideoService.getEditJobsByActivity(activityId, appid);
            return R.ok().put("jobs", jobs);
        } catch (Exception e) {
            return R.error("获取混剪任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询任务状态
     */
    @PostMapping("/batchJobStatus")
    @RequiresPermissions("activity:activityvideo:info")
    public R batchJobStatus(@RequestBody List<String> jobIds, @CookieValue String appid) {
        try {
            Map<String, JobStatusResponse> statusMap = new HashMap<>();
            for (String jobId : jobIds) {
                try {
                    JobStatusResponse response = activityVideoService.getJobStatus(jobId, appid);
                    statusMap.put(jobId, response);
                } catch (Exception e) {
                    // 单个任务查询失败不影响其他任务
                    JobStatusResponse errorResponse = new JobStatusResponse();
                    errorResponse.setJobId(jobId);
                    errorResponse.setStatus("Error");
                    errorResponse.setErrorMessage(e.getMessage());
                    statusMap.put(jobId, errorResponse);
                }
            }
            return R.ok().put("statusMap", statusMap);
        } catch (Exception e) {
            return R.error("批量查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务状态对应的提示信息
     */
    private String getStatusMessage(String status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case "Init":
                return "任务初始化中...";
            case "Processing":
                return "视频混剪处理中，请稍候...";
            case "Success":
                return "视频混剪完成！";
            case "Failed":
                return "视频混剪失败";
            default:
                return "任务状态: " + status;
        }
    }

    /**
     * 获取所有平台配置
     */
    @RequestMapping("/platforms")
    public R getPlatformConfigs() {
        return R.ok().put("platforms", platformMediaConfigService.getAllPlatformConfigs());
    }

    /**
     * 获取平台支持的媒体类型
     */
    @RequestMapping("/platforms/{platform}/mediaTypes")
    public R getPlatformMediaTypes(@PathVariable("platform") String platform) {
        return R.ok().put("mediaTypes", platformMediaConfigService.getPlatformMediaTypes(platform));
    }

    /**
     * 生成成品图片
     * @throws Exception 
     */
    @PostMapping("/generateImages")
    @RequiresPermissions("activity:activityvideo:save")
    @Transactional(rollbackFor = Exception.class)
    public R generateImages(@RequestBody Map<String, Object> params) throws Exception {
            Long activityId = Long.valueOf(params.get("activityId").toString());
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    getUserId(),
                    activityId,
                    3, // 图文成品生成类型
                    null,
                    "生成图文成品"
            );
            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            String platform = (String) params.get("platform");
            String mediaType = (String) params.get("mediaType");
            Integer imageCount = Integer.valueOf(params.get("imageCount").toString());

            // 验证参数
            if (activityId == null || platform == null || !"image".equals(mediaType)) {
                return R.error("参数错误");
            }

            // 验证平台和媒体类型组合
            if (!platformMediaConfigService.isValidPlatformMediaType(platform, mediaType)) {
                return R.error("该平台不支持图片类型");
            }

            // 根据平台查找对应的文案
            ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeNotUse(activityId, platform);
            if (textEntity == null) {
                return R.error("未找到对应平台的文案，请先生成" + platform + "类型的文案");
            }
            List<ActivityImageEntity> selectedImages = activityImageService.findByActivityIdNoUseLimitByPlatform(activityId, platform, imageCount);
            if (selectedImages.isEmpty()) {
                return R.error("没有可用的图片素材，请先上传图片素材");
            }
            // 计算图片总大小
            long totalSize = selectedImages.stream()
                    .mapToLong(ActivityImageEntity::getFileSize)
                    .sum();

            // 创建成品视频记录（用于存储成品图片）
            ActivityVideoEntity finishedVideo = new ActivityVideoEntity();
            finishedVideo.setActivityId(activityId);
            finishedVideo.setType(1); // 成品类型
            finishedVideo.setPlatform(platform);
            finishedVideo.setMediaType(mediaType);
            finishedVideo.setImageCount(imageCount);
            finishedVideo.setUseCount(0);
            finishedVideo.setName(textEntity.getName());
            finishedVideo.setActivityTextId(textEntity.getId()); // 关联文案
                finishedVideo.setFileSize(totalSize);

            // 设置第一张图片的URL作为主要媒体URL
            if (!selectedImages.isEmpty()) {
                finishedVideo.setMediaUrl(selectedImages.get(0).getMediaUrl());
            }

            activityVideoService.save(finishedVideo);

            // 创建图片关联记录
            List<Long> imageIds = selectedImages.stream()
                    .map(ActivityImageEntity::getId)
                    .collect(java.util.stream.Collectors.toList());

            activityVideoConnectService.saveImageConnections(activityId, imageIds, finishedVideo.getId());

            // 增加文案使用次数
            activityTextService.incrementUseCount(textEntity.getId());

            // 增加图片按平台使用次数
            for (ActivityImageEntity image : selectedImages) {
                activityImageService.incrementUseCountByPlatform(image.getId(), platform, activityId);
            }

            return R.ok().put("message", "成功生成成品图片，包含 " + imageCount + " 张图片");

    }

    /**
     * 获取成品视频关联的图片列表
     */
    @GetMapping("/images")
    public R getVideoImages(@RequestParam("videoId") Long videoId) {
        try {
            // 查询video_connect表获取关联的图片
            java.util.List<ActivityVideoConnectEntity> connections =
                activityVideoConnectService.list(new QueryWrapper<ActivityVideoConnectEntity>()
                    .eq("connect_activity_video_id", videoId)
                    .isNotNull("activity_image_id")
                    .orderByAsc("paixu"));

            java.util.List<java.util.Map<String, Object>> images = new java.util.ArrayList<>();

            for (ActivityVideoConnectEntity connection : connections) {
                ActivityImageEntity imageEntity = activityImageService.getById(connection.getActivityImageId());

                if (imageEntity != null) {
                    java.util.Map<String, Object> imageInfo = new java.util.HashMap<>();
                    imageInfo.put("id", imageEntity.getId());
                    imageInfo.put("mediaUrl", imageEntity.getMediaUrl());
                    imageInfo.put("fileSize", imageEntity.getFileSize());
                    imageInfo.put("name", imageEntity.getName());
                    images.add(imageInfo);
                }
            }

            return R.ok().put("images", images);

        } catch (Exception e) {
            return R.error("获取图片列表失败: " + e.getMessage());
        }
    }

}
