package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
@Data
@TableName("activity_video")
@Accessors(chain = true)
public class ActivityVideoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Long id;
	/**
	 * 视频fileID
	 */
	private String fileId;
	/**
	 * 视频名称
	 */
	private String name;
	/**
	 * 文件大小 单位 bit
	 */
	private Long fileSize;
	/**
	 * 视频时长 单位 秒
	 */
	private BigDecimal duration;
	/**
	 * 视频url
	 */
	private String mediaUrl;
	/**
	 * 过期时间
	 */
	private Date expireTime;
	/**
	 * 帧率
	 */
	private BigDecimal frameRate;
	/**
	 * 会议id
	 */
	private Long activityId;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createOn;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long createBy;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	private Date updateOn;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	private Long updateBy;
	/**
	 * 
	 */
	private Integer paixu;
	/**
	 * 0-素材，1-成品
	 */
	private Integer type;
	/**
	 * 使用次数
	 */
	private Integer useCount;
	/**
	 * 使用文案id
	 */
	private Long activityTextId;

	/**
	 * 平台类型（douyin, xiaohongshu, kuaishou等）
	 */
	private String platform;

	/**
	 * 媒体类型（video: 视频, image: 图片）
	 */
	private String mediaType;

	/**
	 * 图片数量（当mediaType为image时使用）
	 */
	private Integer imageCount;

	@TableField(exist = false)
	private String repeatToken;

	@TableField(exist = false)
	private String textResult;
}
