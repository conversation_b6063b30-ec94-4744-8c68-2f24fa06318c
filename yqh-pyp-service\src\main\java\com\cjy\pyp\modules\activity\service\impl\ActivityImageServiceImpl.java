package com.cjy.pyp.modules.activity.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;

import com.cjy.pyp.modules.activity.dao.ActivityImageDao;
import com.cjy.pyp.modules.activity.dao.ActivityImagePlatformUsageDao;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;

import com.cjy.pyp.modules.activity.service.ActivityImageService;
import org.springframework.beans.factory.annotation.Autowired;


@Service("activityImageService")
public class ActivityImageServiceImpl extends ServiceImpl<ActivityImageDao, ActivityImageEntity> implements ActivityImageService {

    @Autowired
    private ActivityImagePlatformUsageDao activityImagePlatformUsageDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String activityId = (String) params.get("activityId");
        IPage<ActivityImageEntity> page = this.page(
                new Query<ActivityImageEntity>().getPage(params),
                new QueryWrapper<ActivityImageEntity>()
                        .eq("activity_id",activityId)
                .orderByDesc("id")
        );

        return new PageUtils(page);
    }

    @Override
    public List<ActivityImageEntity> findByIds(List<Long> ids) {
        return ids.size() > 0 ? this.listByIds(ids) : new ArrayList<>();
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdNoUseLimit(Long activityId,Integer limit) {
        return this.list(new QueryWrapper<ActivityImageEntity>()
                .eq("activity_id", activityId)
                .eq("use_count", 0)
                .orderByAsc("create_on")
                .last("LIMIT " + (limit == null ? 3 : limit)));
    }

    @Override
    public void incrementUseCount(Long textId) {
        ActivityImageEntity entity = this.getById(textId);
        if (entity != null) {
            entity.setUseCount(entity.getUseCount() == null ? 1 : entity.getUseCount() + 1);
            this.updateById(entity);
        }
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdNoUseLimitByPlatform(Long activityId, String platform, Integer limit) {
        // 查询活动下的所有图片
        List<ActivityImageEntity> allImages = this.list(new QueryWrapper<ActivityImageEntity>()
                .eq("activity_id", activityId)
                .orderByAsc("create_on"));

        // 过滤出在指定平台未使用的图片
        List<ActivityImageEntity> availableImages = new ArrayList<>();
        for (ActivityImageEntity image : allImages) {
            Integer usageCount = activityImagePlatformUsageDao.getUsageCount(image.getId(), platform);
            if (usageCount == null || usageCount == 0) {
                availableImages.add(image);
                // 如果达到限制数量，停止添加
                if (limit != null && availableImages.size() >= limit) {
                    break;
                }
            }
        }

        return availableImages;
    }

    @Override
    public void incrementUseCountByPlatform(Long imageId, String platform, Long activityId) {
        activityImagePlatformUsageDao.incrementUsageCount(imageId, platform, activityId);
    }

    @Override
    public Integer getUsageCountByPlatform(Long imageId, String platform) {
        Integer count = activityImagePlatformUsageDao.getUsageCount(imageId, platform);
        return count != null ? count : 0;
    }

}