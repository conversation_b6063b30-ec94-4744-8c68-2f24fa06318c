UPDATE `pyp`.`ad_type_config` SET `type_code` = 'douyin', `type_name` = '抖音', `platform` = '抖音', `content_type` = '短视频', `title_length` = '15字以内，吸引眼球', `content_length` = '50-100字，简洁有力', `topics_count` = 5, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 语言要生动活泼\n- 要有强烈的视觉冲击力\n- 内容要有趣味性和互动性\n- 适合年轻用户群体\n- 要有明确的行动召唤\n- 不要出现emoji表情', `style` = '年轻化、潮流、有趣、互动性强', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含任何#号或话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}\r\n\r\n风格特点：{style}', `sort_order` = 1, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-07-29 15:17:28', `update_by` = NULL WHERE `id` = 1;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'xiaohongshu', `type_name` = '小红书', `platform` = '小红书', `content_type` = '图文', `title_length` = '20字以内，突出亮点', `content_length` = '80-150字，详细描述', `topics_count` = 8, `topics_format` = '带#号，用空格分隔', `requirements` = '- 要有生活化的场景描述\n- 内容要真实可信\n- 要有实用价值和分享价值\n- 适合女性用户群体\n- 要有美感和质感', `style` = '精致、生活化、实用、有美感', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}（每个话题前加#号，用空格分隔）\r\n\r\n风格特点：{style}', `sort_order` = 2, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-06-30 03:40:44', `update_by` = NULL WHERE `id` = 2;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'kuaishou', `type_name` = '快手', `platform` = '快手', `content_type` = '短视频', `title_length` = '18字以内，接地气', `content_length` = '60-120字，通俗易懂', `topics_count` = 6, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 语言要接地气，贴近生活\n- 内容要有人情味\n- 要突出实用性和性价比\n- 适合下沉市场用户\n- 要有真实感和亲和力\n- 不要出现emoji表情', `style` = '接地气、真实、亲民、实用', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含任何#号或话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}\r\n\r\n风格特点：{style}', `sort_order` = 3, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-07-29 15:17:38', `update_by` = NULL WHERE `id` = 3;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'dianping', `type_name` = '大众点评', `platform` = '大众点评', `content_type` = '商户评价', `title_length` = '25字以内，突出体验', `content_length` = '100-200字，详细评价', `topics_count` = 3, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 要有具体的体验描述\n- 内容要客观真实\n- 要突出服务和产品特色\n- 要对其他用户有参考价值', `style` = '客观、详细、实用、有参考价值', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}\r\n\r\n风格特点：{style}', `sort_order` = 4, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-06-30 03:45:09', `update_by` = NULL WHERE `id` = 4;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'meituan', `type_name` = '美团点评', `platform` = '美团点评', `content_type` = '商户评价', `title_length` = '25字以内，突出性价比', `content_length` = '100-200字，详细评价', `topics_count` = 3, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 要突出性价比和优惠信息\n- 内容要有消费建议\n- 要描述具体的产品和服务\n- 要有实用的消费提醒\n- 要对其他消费者有帮助', `style` = '实用、性价比、消费导向、有帮助', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}\r\n\r\n风格特点：{style}', `sort_order` = 5, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-06-30 03:40:44', `update_by` = NULL WHERE `id` = 5;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'weixin', `type_name` = '微信朋友圈', `platform` = '微信朋友圈', `content_type` = '社交分享', `title_length` = '30字以内，引发共鸣', `content_length` = '80-150字，有互动性', `topics_count` = 5, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 要有社交属性和分享价值\n- 内容要引发共鸣和讨论\n- 要适合朋友圈的社交场景\n- 要有一定的情感色彩\n- 要鼓励互动和转发', `style` = '社交化、有共鸣、互动性强、情感化', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}\r\n\r\n风格特点：{style}', `sort_order` = 6, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-06-30 03:40:44', `update_by` = NULL WHERE `id` = 6;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'general', `type_name` = '通用文案', `platform` = '通用', `content_type` = '营销文案', `title_length` = '25字以内，突出核心', `content_length` = '100-200字，全面描述', `topics_count` = 5, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 语言要通俗易懂\n- 内容要有普适性\n- 要突出核心价值\n- 适合多平台使用', `style` = '通用、易懂、有价值、适应性强', `prompt_template` = '请为{platform}平台生成{content_type}文案。\r\n\r\n关键词：{keyword}\r\n{title_section}\r\n要求：\r\n{requirements}\r\n\r\n请以JSON格式返回以下内容（键名使用英文）：\r\n- 标题（title，{title_length}）\r\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\r\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\r\n\r\n重要提醒：\r\n- content字段中不要包含话题标签\r\n- 所有话题标签都应该单独放在topics字段中\r\n- topics格式：{topics_format}\r\n\r\n风格特点：{style}', `sort_order` = 8, `status` = 1, `create_on` = '2025-06-30 03:19:38', `create_by` = NULL, `update_on` = '2025-06-30 03:53:56', `update_by` = NULL WHERE `id` = 7;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'douyin_review', `type_name` = '抖音点评', `platform` = '抖音点评', `content_type` = '商户点评', `title_length` = '18字以内，有话题性', `content_length` = '90字以内，生动有趣', `topics_count` = 6, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 要有真实的体验感受\n- 语言要生动有趣，适合短视频\n- 要有视觉冲击力的描述\n- 要能引起用户互动和关注\n- 要突出商户的特色和亮点', `style` = '生动、有趣、真实体验、互动性强', `prompt_template` = '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含任何#号或话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', `sort_order` = 7, `status` = 1, `create_on` = '2025-06-30 03:53:56', `create_by` = NULL, `update_on` = '2025-06-30 03:53:56', `update_by` = NULL WHERE `id` = 8;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'ctrip_review', `type_name` = '携程点评', `platform` = '携程', `content_type` = '旅游点评', `title_length` = '25字以内，突出体验', `content_length` = '100-200字，详细评价', `topics_count` = 4, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 要有具体的旅游体验描述\n- 内容要客观真实\n- 要突出服务质量和性价比\n- 要有对其他旅客的参考价值\n- 要描述住宿、交通、景点等具体细节', `style` = '专业、详细、实用、有参考价值', `prompt_template` = '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', `sort_order` = 9, `status` = 1, `create_on` = '2025-07-21 14:33:20', `create_by` = NULL, `update_on` = '2025-07-24 03:05:41', `update_by` = NULL WHERE `id` = 9;
UPDATE `pyp`.`ad_type_config` SET `type_code` = 'ctrip_notes', `type_name` = '携程笔记', `platform` = 'ctrip', `content_type` = 'notes', `title_length` = '25字以内，突出体验', `content_length` = '100-200字，详细评价', `topics_count` = 3, `topics_format` = '不带#号，用逗号分隔', `requirements` = '- 要有具体的旅游体验描述\n- 内容要客观真实\n- 要突出服务质量和性价比\n- 要有对其他旅客的参考价值\n- 要描述住宿、交通、景点等具体细节', `style` = '专业、详细、实用、有参考价值', `prompt_template` = '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', `sort_order` = 40, `status` = 1, `create_on` = '2025-07-24 02:30:33', `create_by` = NULL, `update_on` = '2025-07-24 03:41:35', `update_by` = NULL WHERE `id` = 10;
