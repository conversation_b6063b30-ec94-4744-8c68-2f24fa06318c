<template>
  <div>
    <!-- 微信环境提示跳转到浏览器 -->
    <div v-if="isWeixin && showWeixinTip" class="weixin-tip-overlay" @click="hideWeixinTip">
      <div class="weixin-tip-content" @click.stop>
        <div class="tip-header">
          <van-icon name="info-o" size="24" color="#ff6b35" />
          <span class="tip-title">请在浏览器中打开</span>
        </div>
        <div class="tip-body">
          <p>为了获得更好的体验，请点击右上角菜单</p>
          <p>选择"在浏览器中打开"或"在Safari中打开"</p>
        </div>
        <div class="tip-actions">
          <van-button size="small" @click="hideWeixinTip">我知道了</van-button>
          <van-button size="small" type="primary" @click="copyCurrentUrl">复制链接</van-button>
        </div>
      </div>
    </div>

    <!-- 左上角悬浮Logo和名称 -->
    <div class="activity-header-info" v-if="activityInfo.logo || activityInfo.name">
      <img v-if="activityInfo.logo" :src="activityInfo.logo" class="activity-logo" />
      <div v-if="activityInfo.name" class="activity-name">
        {{ activityInfo.name }}
      </div>
    </div>
    <!-- 全局背景 -->

    <img v-if="activityInfo.isFixed" style="width: 100%;position: fixed;" :src="activityInfo.background
      ? activityInfo.background
      : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20230318/5b43f9ade094416aae30a53e9edea4b7.png'
      " alt="" />
    <!-- 进场广告 -->
    <div v-if="planeShow" @click="closePlane" class="ad" :style="activityInfo.adColor
      ? 'background-color: ' + activityInfo.adColor
      : 'background-color: white'
      ">
      <img style="width: 100%" :src="activityInfo.ad" alt="" />
    </div>
    <!-- 背景音乐 -->
    <div v-if="activityInfo.musicUrl" id="audioBase" class="audioBase">
      <div id="audio-btn" :class="onOrOff" @click="parseMusic"></div>
      <audio id="music1" ref="audio" controls="controls" :src="activityInfo.musicUrl" autoplay="autoplay"
        style="display: none">
        浏览器不支持音频播放
      </audio>
    </div>
    <!-- <van-swipe :autoplay="3000" :class="activityId == 1648304873736306690 ? 'animate-flipInX' : ''"> -->
    <van-swipe :autoplay="3000">
      <van-swipe-item v-for="(image, index) in activityInfo.appFileList" :key="index">
        <van-image width="100%" :src="image.url"> </van-image>
      </van-swipe-item>
    </van-swipe>
    <!-- 九宫格===模板1=== -->
    <div style="position: relative" :style="{
      backgroundImage:
        'url(' +
        (activityInfo.background
          ? activityInfo.background
          : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20230318/5b43f9ade094416aae30a53e9edea4b7.png') +
        ')',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    }">

      <!-- <img v-if="!activityInfo.isFixed" style="width: 100%; margin-top: -5px" :src="activityInfo.background
        ? activityInfo.background
        : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20230318/5b43f9ade094416aae30a53e9edea4b7.png'
        " alt="" /> -->
      <div v-if="activityInfo.templateId == 1" style="padding-top: 15px;">


        <!-- 我的小店和团购券区域 -->
        <div class="shop-group-section" v-if="activityInfo.showMyShop || activityInfo.showGroupBuying"
          style="margin-top: 10px;">
          <div class="shop-group-container">
            <!-- 我的小店 -->
            <div class="my-shop-card" v-if="activityInfo.showMyShop" @click="goMyShop()">
              <div class="shop-icon">
                <img :src="activityInfo.logo" alt="我的小店">
              </div>
              <div class="shop-content">
                <div class="shop-title">{{ activityInfo.name }}</div>
                <div class="shop-desc">{{ activityInfo.shopDescription || '商户专属店铺' }}</div>
              </div>
              <div class="shop-arrow">
                <van-icon name="arrow" />
              </div>
            </div>

            <!-- 团购券列表 -->
            <div class="group-buying-section" v-if="activityInfo.showGroupBuying">
              <div class="section-header">
                <div class="section-title">团购券</div>
                <div class="section-more" v-if="groupBuyingCoupons.length > 1">
                  <span class="coupon-count">{{ groupBuyingCoupons.length }}个可选</span>
                </div>
              </div>
              <div class="coupons-container" v-if="groupBuyingCoupons.length > 0">
                <!-- 使用 van-swipe 实现左右滑动 -->
                <van-swipe class="coupon-swipe" :show-indicators="groupBuyingCoupons.length > 1"
                           indicator-color="rgba(255, 107, 53, 0.3)"
                           indicator-active-color="#ff6b35">
                  <van-swipe-item v-for="coupon in groupBuyingCoupons" :key="coupon.id">
                    <div class="coupon-item" @click="jumpToGroupBuying(coupon)">
                      <div class="coupon-image" v-if="coupon.coverImage">
                        <img :src="coupon.coverImage" :alt="coupon.couponName">
                      </div>
                      <div class="coupon-content">
                        <div class="coupon-name">{{ coupon.couponName }}</div>
                        <div class="coupon-price">
                          <span class="current-price">¥{{ coupon.groupPrice }}</span>
                          <span class="original-price" v-if="coupon.originalPrice">¥{{ coupon.originalPrice }}</span>
                        </div>
                        <div class="coupon-platform">{{ getPlatformName(coupon.platformType) }}</div>
                      </div>
                      <div class="coupon-arrow">
                        <van-icon name="arrow" />
                      </div>
                    </div>
                  </van-swipe-item>
                </van-swipe>
              </div>
              <div class="no-coupons" v-else>
                <div class="no-coupons-text">暂无团购券</div>
              </div>
            </div>

            <!-- 携程团购 -->
            <div class="ctrip-group-buying-section" v-if="activityInfo.showCtripGroupBuying">
              <div class="section-header">
                <div class="section-title">携程团购</div>
              </div>
              <div class="ctrip-group-buying-item" @click="goCtripGroupBuying()">
                <div class="ctrip-icon">
                  <img src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250618/39f6bf6ec5b849e3935c200fcf08ef25.jpg" alt="携程团购">
                </div>
                <div class="ctrip-content">
                  <div class="ctrip-title">携程团购优惠</div>
                  <div class="ctrip-desc">精选优质团购产品</div>
                </div>
                <div class="ctrip-arrow">
                  <van-icon name="arrow" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="task-section-card">
          <div class="card-title">发视频/种草</div>
          <van-grid :column-num="3" icon-size="70px" class="template1" :border="false">
            <van-grid-item v-if="activityInfo.showDouyin" icon="https://pyp.yqihua.com/shop/static/icons/douyin.png"
              @click="goDouyin()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">发抖音</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showXiaohongshu"
              icon="https://pyp.yqihua.com/shop/static/icons/redbook.png" @click="goXiaohongshu()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">发小红书</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showKuaishou" icon="https://pyp.yqihua.com/shop/static/icons/kuaishou.png"
              @click="goKuaishou()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">发快手</span>
            </van-grid-item>
            <!-- 携程笔记 -->
            <van-grid-item v-if="activityInfo.showCtripNotes" icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250618/39f6bf6ec5b849e3935c200fcf08ef25.jpg"
              @click="goCtripNotes()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">携程笔记</span>
            </van-grid-item>
          </van-grid>
        </div>
        <div class="task-section-card">
          <div class="card-title">去打卡/点评</div>
          <van-grid :column-num="3" icon-size="70px" class="template1" :border="false">
            <van-grid-item v-if="activityInfo.showDouyindianping"
              icon="https://pyp.yqihua.com/shop/static/icons/dydianping.png" @click="goDouyindianping()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">抖音点评</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showDazhongdianping"
              icon="https://pyp.yqihua.com/shop/static/icons/review.png" @click="goDazhongdianping()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">大众点评</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showMeituandianping"
              icon="https://pyp.yqihua.com/shop/static/icons/meituan.png" @click="goMeituandianping()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">美团点评</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showCtripReview" icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250618/39f6bf6ec5b849e3935c200fcf08ef25.jpg"
              @click="goCtripReview()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">携程点评</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showWechatQr" icon="http://yqhpyp.oss-cn-shanghai.aliyuncs.com/20250724/7787316fe7ef47caa3f508cc1579bcde.png"
              @click="goWechatQrCode()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">关注公众号</span>
            </van-grid-item>
          </van-grid>

        </div>
        <div class="task-section-card">
          <div class="card-title">账号关注</div>
          <van-grid :column-num="3" icon-size="70px" class="template1" :border="false">
            <van-grid-item v-if="activityInfo.showQiyeweixin"
              icon="https://pyp.yqihua.com/shop/static/icons/qywechat.png" @click="goQiyeweixin()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">加企微</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showMiniProgram"
              icon="https://pyp.yqihua.com/shop/static/icons/miniprogram.png" @click="goMiniProgram()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">微信小程序</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showGuanzhukuaishou"
              icon="https://pyp.yqihua.com/shop/static/icons/gzkuaishou.png" @click="goGuanzhukuaishou()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">关注快手</span>
            </van-grid-item>
            <van-grid-item v-if="activityInfo.showGuanzhudouyin"
              icon="https://pyp.yqihua.com/shop/static/icons/gzdouyin.png" @click="goGuanzhudouyin()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">关注抖音</span>
            </van-grid-item>
            <!-- 携程首页 -->
             <van-grid-item v-if="activityInfo.showCtrip"
              icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250618/39f6bf6ec5b849e3935c200fcf08ef25.jpg" @click="goCtrip()">
              <span :style="activityInfo.fontColor ? ('color: ' + activityInfo.fontColor) : ''" class="grid-text"
                slot="text">携程首页</span>
            </van-grid-item>
          </van-grid>
        </div>

      </div>
      <!-- 九宫格===模板2=== -->
      <van-grid :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;'
        : 'background:white;position: absolute;z-index: 99;top: 0;'
        " :column-num="3" icon-size="70px" class="template2 bgStyle" :border="false" :gutter="10"
        v-if="activityInfo.templateId == 2">
        <van-grid-item :class="item.animate ? ' ' + item.animate : ''" v-for="item in cmsList" :key="item.id"
          :icon="item.mobileIcon" @click="cmsTurn(item)" :text="item.title" />
      </van-grid>
      <!-- 九宫格===模板3=== -->
      <div class="template3 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 3">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 || index == 3 || index == 4
          ? 'item-big'
          : 'item-big-four'
          ">
          <div @click="cmsTurn(item)" class="item-child" v-if="index == 0 || index == 3 || index == 4">
            <div :class="item.animate
              ? 'item-child-big ' + item.animate
              : 'item-child-big'
              ">
              <van-image width="120" height="120" :src="item.mobileIcon" />
              <div class="font">{{ item.title }}</div>
            </div>
          </div>
          <div v-else class="item-child" v-for="item1 in item" :key="item1.id">
            <div @click="cmsTurn(item12)" :class="item12.animate
              ? 'item-child-small ' + item12.animate
              : 'item-child-small'
              " v-for="item12 in item1" :key="item12.id">
              <van-image width="60" height="60" :src="item12.mobileIcon" />
              <div class="font">{{ item12.title }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板3-1没背景=== -->
      <div class="template3 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 31">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 || index == 3 || index == 4
          ? 'item-big'
          : 'item-big-four'
          ">
          <div @click="cmsTurn(item)" class="item-child" v-if="index == 0 || index == 3 || index == 4">
            <div :class="item.animate
              ? 'item-child-big ' + item.animate
              : 'item-child-big'
              " :style="{
                backgroundImage: 'url(' + item.mobileIcon + ')',
                backgroundSize: '100% 100%',
              }">
              <!-- <van-image width="120" height="120" :src="item.mobileIcon" />
              <div class="font">{{ item.title }}</div> -->
            </div>
          </div>
          <div v-else class="item-child" v-for="item1 in item" :key="item1.id">
            <div @click="cmsTurn(item12)" :class="item12.animate
              ? 'item-child-small ' + item12.animate
              : 'item-child-small'
              " v-for="item12 in item1" :key="item12.id" :style="{
                backgroundImage: 'url(' + item12.mobileIcon + ')',
                backgroundSize: '100% 100%',
              }">
              <!-- <van-image width="60" height="60" :src="item12.mobileIcon" />
              <div class="font">{{ item12.title }}</div> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板4=== -->
      <div class="bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 4">
        <div class="nav-list">
          <div :class="item.animate ? 'nav-item ' + item.animate : 'nav-item'" @click="cmsTurn(item)"
            v-for="(item, index) in cmsList" :key="index">
            <div class="top" :style="{ backgroundColor: item.color + ' !important' }">
              <img class="image" :src="item.mobileIcon" />
            </div>
            <div class="bottom">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板5=== 纯图标-->
      <van-grid :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " :column-num="3" icon-size="120px" class="template5 bgStyle" :border="false"
        v-if="activityInfo.templateId == 5">
        <van-grid-item :class="item.animate ? ' ' + item.animate : ''" v-for="item in cmsList" :key="item.id"
          :icon="item.mobileIcon" @click="cmsTurn(item)" />
      </van-grid>
      <!-- 九宫格===模板6-1没背景=== -->
      <div class="template6 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 61">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 ||
          index == 1 ||
          index == 2 ||
          index == 5 ||
          index == 6 ||
          index == 7
          ? 'item-three'
          : index == 3 || index == 4
            ? 'item-two'
            : index == 8 || index == 9
              ? 'item-two-small'
              : 'item-one'
          ">
          <div @click="cmsTurn(item)" :class="item.animate ? 'item ' + item.animate : 'item'" :style="{
            backgroundImage: 'url(' + item.mobileIcon + ')',
            backgroundSize: '100% 100%',
          }" v-if="index == 0 ||
            index == 1 ||
            index == 2 ||
            index == 5 ||
            index == 6 ||
            index == 7 ||
            index == 3 ||
            index == 8 ||
            index == 9
          "></div>
          <div class="item" v-if="index == 4">
            <div @click="cmsTurn(item1)" :class="item1.animate ? 'item ' + item1.animate : 'item'" :style="{
              backgroundImage: 'url(' + item1.mobileIcon + ')',
              backgroundSize: '100% 100%',
            }" v-for="item1 in item" :key="item1.id"></div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板6-2没背景=== -->
      <div class="template62 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 62">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 ||
          index == 1 ||
          index == 2 ||
          index == 7 ||
          index == 8 ||
          index == 9
          ? 'item-three'
          : index == 3 || index == 4
            ? 'item-two'
            : index == 5 || index == 6
              ? 'item-two-three'
              : 'item-one'
          ">
          <div @click="cmsTurn(item)" :class="item.animate ? 'item ' + item.animate : 'item'" :style="{
            backgroundImage: 'url(' + item.mobileIcon + ')',
            backgroundSize: '100% 100%',
          }" v-if="index == 0 ||
            index == 1 ||
            index == 2 ||
            index == 5 ||
            index == 6 ||
            index == 7 ||
            index == 3 ||
            index == 8 ||
            index == 9
          "></div>
          <div class="item" style="box-shadow: none" v-if="index == 4">
            <div @click="cmsTurn(item1)" :class="item1.animate ? 'item ' + item1.animate : 'item'" :style="{
              backgroundImage: 'url(' + item1.mobileIcon + ')',
              backgroundSize: '100% 100%',
            }" v-for="item1 in item" :key="item1.id"></div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板6-3没背景=== -->
      <div class="template63 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 63">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 || index == 1 || index == 2
          ? 'item-three'
          : index == 3 || index == 4
            ? 'item-two'
            : index == 5 ||
              index == 6 ||
              index == 7 ||
              index == 8 ||
              index == 9 ||
              index == 10 ||
              index == 11
              ? 'item-two-three'
              : 'item-one'
          ">
          <div @click="cmsTurn(item)" :class="item.animate ? 'item ' + item.animate : 'item'" :style="{
            backgroundImage: 'url(' + item.mobileIcon + ')',
            backgroundSize: '100% 100%',
          }" v-if="index == 0 ||
            index == 1 ||
            index == 2 ||
            index == 5 ||
            index == 6 ||
            index == 7 ||
            index == 3 ||
            index == 8 ||
            index == 9 ||
            index == 10 ||
            index == 11
          "></div>
          <div class="item" style="box-shadow: none" v-if="index == 4">
            <div @click="cmsTurn(item1)" :class="item1.animate ? 'item ' + item1.animate : 'item'" :style="{
              backgroundImage: 'url(' + item1.mobileIcon + ')',
              backgroundSize: '100% 100%',
            }" v-for="item1 in item" :key="item1.id"></div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板6-4没背景=== -->
      <div class="template64 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 20px;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 20px;width: 100%;'
        " v-if="activityInfo.templateId == 64">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 || index == 1 || index == 2 || index == 5 ||
          index == 6 ||
          index == 7
          ? 'item-three'
          : index == 3 || index == 4
            ? 'item-two'
            :
            index == 8 ||
              index == 9 ||
              index == 10 ||
              index == 11
              ? 'item-two-three'
              : 'item-one'
          ">
          <div @click="cmsTurn(item)" :class="item.animate ? 'item ' + item.animate : 'item'" :style="{
            backgroundImage: 'url(' + item.mobileIcon + ')',
            backgroundSize: '100% 100%',
          }" v-if="index == 0 ||
            index == 1 ||
            index == 2 ||
            index == 3 ||
            index == 5 ||
            index == 6 ||
            index == 7 ||
            index == 8 ||
            index == 9 ||
            index == 10 ||
            index == 11
          "></div>
          <div class="item" style="box-shadow: none" v-if="index == 4">
            <div @click="cmsTurn(item1)" :class="item1.animate ? 'item ' + item1.animate : 'item'" :style="{
              backgroundImage: 'url(' + item1.mobileIcon + ')',
              backgroundSize: '100% 100%',
            }" v-for="item1 in item" :key="item1.id"></div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板6-6没背景=== -->
      <div class="template64 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 20px;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 20px;width: 100%;'
        " v-if="activityInfo.templateId == 66">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 || index == 1 || index == 2 || index == 5 ||
          index == 6
          ? 'item-three'
          : index == 3 || index == 4
            ? 'item-two'
            :
            index == 8 ||
              index == 9 ||
              index == 10 ||
              index == 11
              ? 'item-two-three'
              : 'item-one'
          ">
          <div @click="cmsTurn(item)" :class="item.animate ? 'item ' + item.animate : 'item'" :style="{
            backgroundImage: 'url(' + item.mobileIcon + ')',
            backgroundSize: '100% 100%',
          }" v-if="index == 0 ||
            index == 1 ||
            index == 2 ||
            index == 3 ||
            index == 5 ||
            index == 6 ||
            index == 7 ||
            index == 8 ||
            index == 9 ||
            index == 10 ||
            index == 11
          "></div>
          <div class="item" style="box-shadow: none" v-if="index == 4">
            <div @click="cmsTurn(item1)" :class="item1.animate ? 'item ' + item1.animate : 'item'" :style="{
              backgroundImage: 'url(' + item1.mobileIcon + ')',
              backgroundSize: '100% 100%',
            }" v-for="item1 in item" :key="item1.id"></div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板6-5 自定义颜色 & 图标=== -->
      <div class="template64 bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 20px;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 20px;width: 100%;'
        " v-if="activityInfo.templateId == 65">
        <div v-for="(item, index) in cmsList" :key="index" :class="index == 0 || index == 1 || index == 2
          ? 'item-three'
          : index == 3 || index == 4
            ? 'item-two'
            : index == 5 ||
              index == 6 ||
              index == 7 ||
              index == 8 ||
              index == 9 ||
              index == 10 ||
              index == 11
              ? 'item-two-three'
              : 'item-one'
          ">
          <div @click="cmsTurn(item)"
            :class="(item.animate ? 'item ' + item.animate : 'item') + ' gradient-deep-' + item.realindex" v-if="index == 0 ||
              index == 1 ||
              index == 2 ||
              index == 5 ||
              index == 6 ||
              index == 7 ||
              index == 3 ||
              index == 8 ||
              index == 9 ||
              index == 10 ||
              index == 11
            ">
            <img class="image" :src="item.mobileIcon" />
            <div class="title">{{ item.title }}</div>
          </div>
          <div class="item" style="box-shadow: none" v-if="index == 4">
            <div @click="cmsTurn(item1)"
              :class="(item1.animate ? 'item ' + item1.animate : 'item') + ' gradient-deep-' + item1.realindex"
              v-for="item1 in item" :key="item1.id">
              <img class="image" :src="item1.mobileIcon" />
              <div class="title">{{ item1.title }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板7=== -->
      <div class="bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 7">
        <div class="nav-list">
          <div
            :class="item.animate ? ('template7 ' + item.animate + ' gradient-deep-' + index) : ('template7 ' + ' gradient-deep-' + index)"
            @click="cmsTurn(item)" v-for="(item, index) in cmsList" :key="index">
            <!-- <div v-if="item.mobileIcon" class="top" :style="{ backgroundColor: item.color + ' !important' }"> -->
            <img class="image" :src="item.mobileIcon" />
            <!-- </div> -->
            <div class="bottom">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <!-- 九宫格===模板7-1 纯背景=== -->
      <div class="bgStyle" :style="activityInfo.background
        ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
        : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
        " v-if="activityInfo.templateId == 71">
        <div class="nav-list">
          <div
            :class="item.animate ? ('template7 ' + item.animate + ' gradient-deep-' + index) : ('template7 ' + ' gradient-deep-' + index)"
            @click="cmsTurn(item)" v-for="(item, index) in cmsList" :key="index" :style="{
              backgroundImage: 'url(' + item.mobileIcon + ')',
              backgroundSize: '100% 100%',
            }">
            <!-- <img class="image" :src="item.mobileIcon" /> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 九宫格底部-文件下载 -->
    <div class="download" v-if="activityInfo.type &&
      activityInfo.type.includes('2') &&
      activityBottom[2]
    " style="margin-top: 10px">
      <van-collapse v-model="downActive">
        <van-collapse-item title="文件下载" name="0">
          <div v-for="item in activityBottom[2]" :key="item.id">
            <van-cell :title="item.name" v-if="item.type == 2" @click="download(item.url)" value="点击下载" />
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <!-- 九宫格底部-banner广告 -->
    <div v-if="activityInfo.type &&
      activityInfo.type.includes('1') &&
      activityBottom[1]
    ">
      <van-swipe :autoplay="3000">
        <div v-for="(image, index) in activityBottom[1]" :key="index">
          <van-swipe-item v-if="image.type == 1">
            <van-image width="100%" height="100px" :src="image.name" @click="openUrl(image.url)">
            </van-image>
          </van-swipe-item>
        </div>
      </van-swipe>
    </div>
    <!-- 九宫格底部-自定义 -->
    <div class="bottomdiy" :style="{ backgroundColor: activityInfo.bottomColor }" v-if="activityInfo.type &&
      activityInfo.type.includes('0') &&
      activityBottom[0]
    ">
      <div v-for="(item, index) in activityBottom[0]" :key="index">
        <a class="item" :href="item.url">{{
          item.name
        }}</a>
      </div>
    </div>
    <!-- <div class="copyright"><a href="https://beian.miit.gov.cn/">蒙ICP备2021004073号-1</a></div> -->
    <sub-modal :show="showFollowModal" :qrcodeImgUrl="subUrl" @close="showFollowModal = false"></sub-modal>
    <img class="back" v-if="!userInfo.subscribe &&
      activityInfo.showSub == 1 &&
      activityInfo.appid == 'wx0770d56458b33c67'
    " @click="showFollowModal = true"
      src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/0b7689735164454a876666e00f2272d9.png" alt="" />
    <img class="back" v-else-if="!userInfo.subscribe && activityInfo.showSub == 1" @click="showFollowModal = true"
      src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/a040f0b471a34b178e83d94ab937476d.png" alt="" />
    <img class="back" v-if="activityId == 1831530102796124161 || activityId == 1831530440760557569"
      @click="turnActivity('1838054778414366721')" :src="activityInfo.backImg" alt="" />

    <!-- 点评内容预览对话框 -->
    <van-dialog v-model="showReviewDialog" :title="reviewData.platform + '内容预览'" show-cancel-button
      confirm-button-text="确认跳转" cancel-button-text="关闭" @confirm="confirmReviewJump" @cancel="showReviewDialog = false"
      class="review-dialog">
      <div class="review-content">
        <!-- 初始加载状态 -->
        <div class="loading-state" v-if="reviewLoading">
          <van-loading type="spinner" color="#1989fa" size="24px">
            正在获取内容...
          </van-loading>
        </div>

        <!-- 内容区域 -->
        <div v-else>
        <!-- 文案内容 -->
        <div class="review-text-section" v-if="reviewData.title || reviewData.content">
          <div class="section-title">
            <van-icon name="edit" />
            <span>文案内容</span>
          </div>

          <div class="text-content">
            <div class="text-title" v-if="reviewData.title">
              <strong>标题：</strong>{{ reviewData.title }}
            </div>
            <div class="text-body" v-if="reviewData.content">
              <strong>内容：</strong>
              <p>{{ reviewData.content }}</p>
            </div>
            <div class="text-keyword" v-if="reviewData.promptKeyword">
              <strong>关键词：</strong>{{ reviewData.promptKeyword }}
            </div>
          </div>

          <div class="image-actions">
            <van-button size="small" type="primary" @click="copyReviewText">
              <van-icon name="copy" />
              复制文案
            </van-button>
            <van-button size="small" type="info" plain @click="regenerateReviewText" :loading="regenerating">
              <van-icon name="refresh" />
              换一篇
            </van-button>
            <!-- <van-button size="small" type="info" plain @click="saveReviewText" v-if="reviewData.textId">
              <van-icon name="bookmark-o" />
              保存文案
            </van-button> -->
          </div>
        </div>

        <!-- 图片内容 -->
        <div class="review-image-section" v-if="reviewData.hasImages && reviewData.images.length > 0">
          <div class="section-title">
            <van-icon name="photo" />
            <span>配图素材</span>
          </div>

          <div class="image-gallery">
            <div v-for="(image, index) in reviewData.images" :key="index" class="image-item"
              @click="previewImage(index)">
              <van-image :src="image.mediaUrl || image.url" fit="cover" class="review-image" />
            </div>
          </div>

          <div class="image-actions">
            <van-button size="small" type="info" @click="saveReviewImages">
              <van-icon name="down" />
              长按图片，保存，点击“确认跳转”点评打卡
            </van-button>
            <!-- <van-button size="small" type="info" plain @click="openImagesInNewTabs">
              <van-icon name="photo-o" />
              新窗口打开
            </van-button>
            <van-button size="small" type="default" plain @click="copyImageUrls">
              <van-icon name="copy" />
              复制链接
            </van-button> -->
          </div>
        </div>

        <!-- 无内容提示 -->
        <div class="no-content" v-if="!reviewData.title && !reviewData.content && !reviewData.hasImages">
          <van-empty description="暂无相关内容">
            <van-button type="primary" size="small">
              暂无相关内容
            </van-button>
          </van-empty>
        </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { isURL, isWeixin } from "@/js/validate";
import date from "@/js/date.js";
import deviceVersion from "@/js/deviceVersion.js";
export default {
  components: {},
  data() {
    return {
      from: '',
      planeShow: 0,
      openid: undefined,
      activityId: undefined,
      downActive: ["0"],
      cmsList: [],
      activityBottom: {},
      bannerIndex: 0,
      activityInfo: {},
      dateCompare: 0,
      onOrOff: "on",
      showFollowModal: false,
      subUrl: "",
      showSub: 0,
      userInfo: {},
      adTimer: null,
      // 微信环境提示相关
      isWeixin: isWeixin(),
      showWeixinTip: false,
      // 点评相关数据
      showReviewDialog: false,
      reviewData: {
        platform: '',
        platformCode: '',
        title: '',
        content: '',
        images: [],
        hasImages: false,
        textId: null,
        promptKeyword: ''
      },
      reviewLoading: false,
      regenerating: false,
      currentPlatform: '',
      pollingTimer: null,
      // 团购券相关数据
      groupBuyingCoupons: [],
    };
  },
  components: {
    SubModal: () => import("@/components/SubModal"),
  },
  mounted() {
    this.openid = this.$cookie.get("openid");
    this.activityId = this.$route.query.id;
    this.from = this.$route.query.from || '';
    this.getActivityInfo();
    this.activityLogCount();
    this.subUrl = this.$cookie.get("subUrl");

    // 检测微信环境并显示提示
    this.checkWeixinEnvironment();

    // 如果是快手
    if (this.from == 'kuai') {
      this.genKuaiShare();
    }
  },
  methods: {
    genKuaiShare() {
      // 跳出一个弹窗，发布视频
      var that = this;
      let url = window.location.href.split("#");
      let realUrl = url[0] + "#/cms/kuaiShare?id=" + this.activityId
      vant.Dialog.alert({
        title: "提示",
        message: '点击确认，跳转发布视频',
      }).then(() => {
        that.from = '';

        // 添加短暂延时确保弹窗完全关闭
        setTimeout(() => {
          window.open('https://open.kuaishou.com/oauth2/authorize?app_id=ks712131693718308739&scope=user_video_publish&response_type=code&redirect_uri=' +
            encodeURIComponent(realUrl), "_self");
        }, 300);
      });
    },
    goDouyin() {
      const that = this;

      // 显示加载提示
      const loading = vant.Toast.loading({
        message: '正在准备分享...',
        forbidClick: true,
        duration: 0
      });

      // 调用后端接口获取抖音分享Schema
      this.$fly.get('/pyp/web/douyin/getShareSchema', {
        activityId: this.activityId
        // videoType和shareToPublish使用后端配置的默认值
      }).then(function (res) {
        loading.clear();

        if (res.code === 200 && res.result && res.result.schema) {
          // 根据官方文档，直接使用Schema拉起抖音应用
          // 支持两种方式：1. 移动端H5直接拉起 2. 生成二维码扫描
          const schema = res.result.schema;

          // 检测是否为移动设备
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

          if (isMobile) {
            // 移动端直接拉起抖音应用
            window.location.href = schema;
          } else {
            // PC端显示二维码或提示
            that.showDouyinQRCode(schema);
          }
        } else {
          vant.Toast(res.msg || "暂无可分享的视频内容");
        }
      }).catch(function (err) {
        loading.clear();
        console.error('获取抖音分享Schema异常:', err);
        // 降级处理：如果有配置的抖音链接，则跳转
        if (that.activityInfo.douyin) {
          window.open(that.activityInfo.douyin, "_self");
        } else {
          vant.Toast("获取分享链接失败，请稍后重试");
        }
      });
    },

    // 显示抖音二维码（PC端使用）
    showDouyinQRCode(schema) {
      // 这里可以生成二维码或者显示提示信息
      vant.Dialog.alert({
        title: '抖音分享',
        message: '请在手机上打开此页面，或使用抖音扫描二维码进行分享',
        confirmButtonText: '我知道了'
      });

      // 可以选择复制Schema到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(schema).then(() => {
          vant.Toast('分享链接已复制到剪贴板');
        });
      }
    },
    goXiaohongshu() {
      console.log('开始小红书分享');

      // 保存this引用，以便在回调函数中使用
      const that = this;

      // 调用后端接口获取SDK初始化参数和分享内容
      this.$fly.get('/pyp/web/xiaohongshu/config', {
        activityId: this.activityId
      }).then(function (res) {
        if (res.code === 200) {
          const config = res;
          console.log('获取到小红书配置和分享内容:', config);

          // 根据小红书最新JS SDK文档，直接调用share方法
          xhs.share({
            shareInfo: {
              type: 'normal',  // 普通分享类型
              title: config.title || that.activityInfo.name || '活动分享', // 使用后端返回的标题
              content: config.content || '助力商家获客营销', // 使用后端返回的内容
              images: config.images && config.images.length > 0 ? config.images :
                [that.activityInfo.shareUrl ? that.activityInfo.shareUrl :
                  (that.activityInfo.mobileBanner ? that.activityInfo.mobileBanner.split(",")[0] : '')], // 优先使用后端返回的图片
            },
            verifyConfig: {
              appKey: config.appKey,
              nonce: config.nonceStr,
              timestamp: config.timestamp,
              signature: config.signature
            },
            success: function (res) {
              console.log('小红书分享成功:', res);
              vant.Toast('分享成功');
            },
            cancel: function () {
              console.log('用户取消分享');
              vant.Toast('分享取消');
            },
            fail: function (error) {
              console.error('小红书分享失败:', error);
              vant.Toast('分享失败: ' + (error.message || '未知错误'));
              // 分享失败时，降级为直接跳转
              // window.open(that.activityInfo.xiaohongshu, "_self");
            }
          });
        } else {
          console.error('获取小红书配置失败:', res.msg);
          vant.Toast(res.msg || '获取配置失败');
          // 获取配置失败时，降级为直接跳转
          // window.open(that.activityInfo.xiaohongshu, "_self");
        }
      }).catch(function (err) {
        console.error('获取小红书配置异常:', err);
        vant.Toast('网络请求失败，请检查网络连接');
        // 异常时，降级为直接跳转
        // window.open(that.activityInfo.xiaohongshu, "_self");
      });
    },
    goDouyindianping() {
      this.showReviewContent('douyin', () => {
        if (!this.activityInfo.douyindianping) {
          vant.Toast("暂未设置抖音点评");
          return;
        }
        window.open('snssdk1128://poi/detail?id=' + this.activityInfo.douyindianping, "_self");
      });
    },
    goKuaishou() {
      // if(!this.activityInfo.zhuyeKuaishou) {
      //   vant.Toast("暂未设置快手");
      //   return;
      // }
      // 当前网页做decode
      let url = encodeURIComponent(window.location.href + "&from=kuai");
      console.log(url)
      location.href = 'kwai://webview?url=' + url;
    },
    goGuanzhudouyin() {
      if (!this.activityInfo.zhuyeDouyin) {
        vant.Toast("暂未设置抖音关注");
        return;
      }
      window.open('snssdk1128://user/profile/' + this.activityInfo.zhuyeDouyin + '?refer=web&gd_label=click_wap_download_follow&type=need_follow&needlaunchlog=1', "_self");
    },
    goGuanzhukuaishou() {
      if (!this.activityInfo.guanzhukuaishou) {
        vant.Toast("暂未设置快手关注");
        return;
      }
      window.open(this.activityInfo.guanzhukuaishou, "_self");
    },
    // 携程点评 - 显示弹窗
    goCtripReview() {
      if (!this.activityInfo.ctripReviewConfig) {
        vant.Toast("暂未设置携程点评");
        return;
      }
      this.showReviewContent('ctrip_review', () => {
        this.$fly
          .get(`/pyp/web/miniprogram/generateScheme`, {
            activityId: this.activityId,
            type: 'ctrip_review',
            path: 'pages/components/pages/miniapp-jump/ctrip'
          })
          .then((res) => {
            this.loading = false;
            if (res.code == 200) {
              const scheme = res.scheme;
              console.log('生成的URL Scheme:', scheme);

              vant.Toast("正在打开小程序...");
              window.location.href = scheme;

            } else {
              vant.Toast(res.msg);
            }
          });
      });
    },
    goCtrip() {
      if (!this.activityInfo.ctripConfig) {
        vant.Toast("暂未设置携程首页");
        return;
      }
        this.$fly
          .get(`/pyp/web/miniprogram/generateScheme`, {
            activityId: this.activityId,
            type: 'ctrip',
            path: 'pages/components/pages/miniapp-jump/ctrip'
          })
          .then((res) => {
            this.loading = false;
            if (res.code == 200) {
              const scheme = res.scheme;
              console.log('生成的URL Scheme:', scheme);

              vant.Toast("正在打开小程序...");
              window.location.href = scheme;

            } else {
              vant.Toast(res.msg);
            }
          });
    },

    // 携程笔记 - 显示弹窗
    goCtripNotes() {
      if (!this.activityInfo.ctripNotesConfig) {
        vant.Toast("暂未设置携程笔记");
        return;
      }
      this.showReviewContent('ctrip_notes', () => {
        this.$fly
          .get(`/pyp/web/miniprogram/generateScheme`, {
            activityId: this.activityId,
            type: 'ctrip_notes',
            path: 'pages/components/pages/miniapp-jump/ctrip'
          })
          .then((res) => {
            this.loading = false;
            if (res.code == 200) {
              const scheme = res.scheme;
              console.log('生成的URL Scheme:', scheme);

              vant.Toast("正在打开小程序...");
              window.location.href = scheme;

            } else {
              vant.Toast(res.msg);
            }
          });
      });
    },

    showWechatQrModal() {
      // 显示微信公众号二维码的放大版本
      vant.ImagePreview([this.activityInfo.wechatQrCode]);
    },
    goDazhongdianping() {
      if (!this.activityInfo.dazhongdianping) {
        vant.Toast("暂未设置大众点评");
        return;
      }
      this.showReviewContent('dianping', () => {
        window.open('dianping://shopinfo?id=' + this.activityInfo.dazhongdianping, "_self");
      });
    },
    goMeituandianping() {
      if (!this.activityInfo.meituan) {
        vant.Toast("暂未设置美团点评");
        return;
      }
      this.showReviewContent('meituan', () => {
        window.open('imeituan://www.meituan.com/food/poi/detail?id=' + this.activityInfo.meituan, "_self");
      });
    },
    goQiyeweixin() {
      if (!this.activityInfo.qiyeweixin) {
        // 提示
        vant.Toast("暂未设置企业微信");
        return;
      }
      //
      window.open('weixin://biz/ww/profile/' + this.activityInfo.qiyeweixin, "_self");
    },
    async goMiniProgram() {
      try {
        // 检查是否在微信内且有wx API
        const userAgent = navigator.userAgent.toLowerCase();
        const isWeChat = userAgent.includes('micromessenger') && !userAgent.includes('wxwork');
        const hasWxAPI = typeof wx !== 'undefined' && wx.navigateToMiniProgram;

        if (isWeChat && hasWxAPI) {
          // 微信内使用原生API跳转
          console.log('使用微信内跳转API');
          vant.Toast("正在跳转小程序...");

          wx.navigateToMiniProgram({
            targetAppId: 'wxdb9c8640799c307b',
            path: 'pages/index/index',
            extraData: {
              from: 'h5',
              activityId: this.activityId,
              timestamp: Date.now()
            },
            envVersion: 'release',
            success: (res) => {
              console.log('微信内跳转成功:', res);
              vant.Toast("跳转成功");
            },
            fail: (err) => {
              console.error('微信内跳转失败:', err);
              vant.Toast("跳转失败，请稍后重试");
            }
          });
        } else {
          // 非微信环境使用后端生成的URL Scheme跳转
          console.log('使用URL Scheme跳转');
          vant.Toast("正在生成跳转链接...");

          this.$fly
            .get(`/pyp/web/miniprogram/generateScheme`, {
              activityId: this.activityId,
              path: 'pages/components/pages/miniapp-jump/otherminiapp'
            })
            .then((res) => {
              this.loading = false;
              if (res.code == 200) {
                const scheme = res.scheme;
                console.log('生成的URL Scheme:', scheme);

                vant.Toast("正在打开小程序...");
                window.location.href = scheme;

              } else {
                vant.Toast(res.msg);
              }
            });
        }

      } catch (error) {
        console.error('跳转小程序失败:', error);
        vant.Toast(error.message || "跳转小程序失败，请稍后重试");
      }
    },
    async goMyShop() {
      try {
        // 检查小店类型
        if (this.activityInfo.shopType === 0) {
          // 网页类型
          if (!this.activityInfo.shopUrl) {
            vant.Toast("暂未设置小店网页地址");
            return;
          }
          // 打开网页
          window.open(this.activityInfo.shopUrl, "_blank");
        } else {
          // 小程序类型
          if (!this.activityInfo.shopAppid || !this.activityInfo.shopPagePath) {
            vant.Toast("暂未设置小店小程序信息");
            return;
          }

          // 检查是否在微信内且有wx API
          const userAgent = navigator.userAgent.toLowerCase();
          const isWeChat = userAgent.includes('micromessenger') && !userAgent.includes('wxwork');
          const hasWxAPI = typeof wx !== 'undefined' && wx.navigateToMiniProgram;

          if (isWeChat && hasWxAPI) {
            // 微信内使用原生API跳转
            console.log('使用微信内跳转API到小店小程序');
            vant.Toast("正在跳转小程序...");

            wx.navigateToMiniProgram({
              targetAppId: this.activityInfo.shopAppid,
              path: this.activityInfo.shopPagePath,
              extraData: {
                from: 'h5',
                activityId: this.activityId,
                timestamp: Date.now()
              },
              envVersion: 'release',
              success: (res) => {
                console.log('微信内跳转成功:', res);
                vant.Toast("跳转成功");
              },
              fail: (err) => {
                console.error('微信内跳转失败:', err);
                vant.Toast("跳转失败，请稍后重试");
              }
            });
          } else {
            // 非微信环境使用后端生成的URL Scheme跳转
            console.log('使用URL Scheme跳转到小店小程序');
            vant.Toast("正在生成跳转链接...");

          this.$fly
            .get(`/pyp/web/miniprogram/generateScheme`, {
              activityId: this.activityId,
              path: 'pages/components/pages/miniapp-jump/otherminiapp'
            })
            .then((res) => {
              this.loading = false;
              if (res.code == 200) {
                const scheme = res.scheme;
                console.log('生成的URL Scheme:', scheme);

                vant.Toast("正在打开小程序...");
                window.location.href = scheme;

              } else {
                vant.Toast(res.msg);
              }
            });
          }
        }
      } catch (error) {
        console.error('跳转小店失败:', error);
        vant.Toast(error.message || "跳转小店失败，请稍后重试");
      }
    },

    goGroupBuying() {
      vant.Toast("正在加载团购券...");

      // 获取团购券列表
      this.$fly.get('/pyp/web/groupbuying/coupons', {
        activityId: this.activityId
      }).then((res) => {
        if (res.code !== 200) {
          vant.Toast(res.msg || "获取团购券失败");
          return;
        }

        const coupons = res.coupons;
        if (!coupons || coupons.length === 0) {
          vant.Toast("暂无可用团购券");
          return;
        }

        // 如果只有一个团购券，直接跳转
        if (coupons.length === 1) {
          this.jumpToGroupBuying(coupons[0]);
          return;
        }

        // 多个团购券，显示选择列表
        this.showGroupBuyingList(coupons);
      }).catch((error) => {
        console.error('获取团购券失败:', error);
        vant.Toast("获取团购券失败，请稍后重试");
      });
    },

    showGroupBuyingList(coupons) {
      // 构建选择项
      const actions = coupons.map(coupon => ({
        name: `${coupon.couponName} - ¥${coupon.groupPrice}`,
        coupon: coupon
      }));

      // 显示选择弹窗
      vant.ActionSheet.show({
        title: '选择团购券',
        actions: actions,
        onSelect: (action) => {
          this.jumpToGroupBuying(action.coupon);
        }
      });
    },

    jumpToGroupBuying(coupon) {
      vant.Toast("正在跳转...");

      // 记录点击
      this.$fly.post('/pyp/web/groupbuying/click', {
        couponId: coupon.id
      }).catch(() => { }); // 忽略统计错误

      // 检测设备环境
      const userAgent = navigator.userAgent.toLowerCase();
      const isApp = this.isInApp(coupon.platformType, userAgent);

      // 获取跳转链接
      this.$fly.get('/pyp/web/groupbuying/jumpUrl', {
        couponId: coupon.id,
        isApp: isApp
      }).then((res) => {
        if (res.code !== 200) {
          vant.Toast(res.msg || "获取跳转链接失败");
          return;
        }

        const jumpUrl = res.jumpUrl;
        window.location.href = jumpUrl;
      }).catch((error) => {
        console.error('跳转团购券失败:', error);
        vant.Toast("跳转失败，请稍后重试");
      });
    },

    isInApp(platformType, userAgent) {
      // 检测是否在对应APP内
      switch (platformType) {
        case 'douyin':
          return userAgent.includes('aweme') || userAgent.includes('tiktok');
        case 'meituan':
          return userAgent.includes('meituan');
        case 'dianping':
          return userAgent.includes('dianping');
        default:
          return false;
      }
    },

    // 获取团购券列表
    getGroupBuyingCoupons() {
      if (!this.activityInfo.showGroupBuying) {
        return;
      }

      this.$fly.get('/pyp/web/groupbuying/coupons', {
        activityId: this.activityId
      }).then((res) => {
        if (res.code === 200) {
          this.groupBuyingCoupons = res.coupons || [];
        }
      }).catch((error) => {
        console.error('获取团购券失败:', error);
      });
    },

    // 获取平台名称
    getPlatformName(platformType) {
      const platformNames = {
        'douyin': '抖音团购',
        'meituan': '美团团购',
        'dianping': '大众点评团购'
      };
      return platformNames[platformType] || platformType;
    },



    async goWechatQrCode() {
      // 是否配置公众号图片
      if (!this.activityInfo.wechatQrCode) {
        vant.Toast("暂未设置公众号二维码");
        return;
      }

      try {
        // 检查是否在微信内且有wx API
        const userAgent = navigator.userAgent.toLowerCase();
        const isWeChat = userAgent.includes('micromessenger') && !userAgent.includes('wxwork');
        const hasWxAPI = typeof wx !== 'undefined' && wx.navigateToMiniProgram;

        if (isWeChat && hasWxAPI) {
          // 微信内使用原生API跳转
          console.log('使用微信内跳转API到二维码页面');
          vant.Toast("正在跳转小程序...");

          wx.navigateToMiniProgram({
            targetAppId: 'wxdb9c8640799c307b',
            path: 'pages/components/pages/qrcode/qrcode',
            extraData: {
              from: 'h5',
              id: this.activityId,
              timestamp: Date.now()
            },
            envVersion: 'release',
            success: (res) => {
              console.log('微信内跳转成功:', res);
              vant.Toast("跳转成功");
            },
            fail: (err) => {
              console.error('微信内跳转失败:', err);
              vant.Toast("跳转失败，请稍后重试");
            }
          });
        } else {
          // 非微信环境使用后端生成的URL Scheme跳转
          console.log('使用URL Scheme跳转到二维码页面');
          vant.Toast("正在生成跳转链接...");

          this.$fly
            .get(`/pyp/web/miniprogram/generateScheme`, {
              activityId: this.activityId,
              path: 'pages/components/pages/qrcode/qrcode'
            })
            .then((res) => {
              this.loading = false;
              if (res.code == 200) {
                const scheme = res.scheme;
                console.log('生成的URL Scheme:', scheme);

                vant.Toast("正在打开小程序...");
                window.location.href = scheme;

              } else {
                vant.Toast(res.msg);
              }
            });

        }

      } catch (error) {
        console.error('跳转小程序失败:', error);
        vant.Toast(error.message || "跳转小程序失败，请稍后重试");
      }
    },
    closePlane() {
      this.planeShow = 0;
      sessionStorage.setItem("planeShow", "false");
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            if (this.activityInfo.showSub == 1) {
              this.getUserInfo();
            }
            document.title = this.activityInfo.name;
            if (this.activityInfo.ad) {
              let show = sessionStorage.getItem("planeShow");
              console.log(show)
              if (!show || show != "false") {
                this.planeShow = 1;
                if (this.activityInfo.adTime) {
                  this.adTimer = setTimeout(() => {
                    this.planeShow = 0;
                    // 处理倒计时
                    this.dealActivity();
                    sessionStorage.setItem("planeShow", "false");
                  }, this.activityInfo.adTime * 1000);
                } else {
                  this.dealActivity();
                }
              } else {
                this.dealActivity();
              }
            } else {
              this.dealActivity();
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    dealActivity() {
      this.activityInfo.backImg =
        this.activityInfo.backImg ||
        "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
      this.getCmsList();
      this.getActivityBottom();
      this.getGroupBuyingCoupons();
      this.$wxShare(
        this.activityInfo.name,
        (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
        '助力商家获客营销'
      ); //加载微信分享

    },
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
          if (!res.data.subscribe) {
            this.showFollowModal = true;
          }
        } else {
          vant.Toast(res.msg);
        }
      });
    },
    // pv，uv记录
    activityLogCount() {
      this.$fly
        .post(`/pyp/activity/activityviewlog/count`, {
          activityId: this.activityId,
          device: deviceVersion.getVersion(),
        })
        .then((res) => { });
    },
    // 获取底部
    getActivityBottom() {
      this.$fly
        .get(
          `/pyp/web/activity/activitybottom/findByActivity/${this.activityId}`,
          {
            type: this.activityInfo.type,
          }
        )
        .then((res) => {
          if (res.code == 200) {
            this.activityBottom = res.result;
          }
        });
    },
    getCmsList() {
      this.$fly
        .get(`/pyp/cms/cms/findByActivityId/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            if (
              this.activityInfo.templateId == 3 ||
              this.activityInfo.templateId == 31
            ) {
              var one = [];
              var one12 = [];
              var one1 = [];
              var one2 = [];
              var one34 = [];
              var one3 = [];
              var one4 = [];
              res.result.forEach((item, index) => {
                if (index == 0 || index == 9 || index == 10) {
                  one.push(item);
                }
                if (index == 1 || index == 2) {
                  one1.push(item);
                }
                if (index == 3 || index == 4) {
                  one2.push(item);
                }
                if (index == 5 || index == 6) {
                  one3.push(item);
                }
                if (index == 7 || index == 8) {
                  one4.push(item);
                }
              });
              if (one1.length > 0) {
                one12.push(one1);
              }
              if (one2.length > 0) {
                one12.push(one2);
              }
              if (one3.length > 0) {
                one34.push(one3);
              }
              if (one4.length > 0) {
                one34.push(one4);
              }
              if (one[0]) {
                if (one12.length > 0) {
                  one.splice(1, 0, one12);
                }
                if (one34.length > 0) {
                  one.splice(2, 0, one34);
                }
              }
              this.cmsList = one;
            } else if (
              this.activityInfo.templateId == 6 ||
              this.activityInfo.templateId == 61 ||
              this.activityInfo.templateId == 62 ||
              this.activityInfo.templateId == 63 ||
              this.activityInfo.templateId == 64 ||
              this.activityInfo.templateId == 66 ||
              this.activityInfo.templateId == 65
            ) {
              var one = [];
              var one2 = [];
              res.result.forEach((item, index) => {
                item.realindex = index;
                if (
                  index == 0 ||
                  index == 1 ||
                  index == 2 ||
                  index == 3 ||
                  index == 6 ||
                  index == 7 ||
                  index == 8 ||
                  index == 9 ||
                  index == 10
                ) {
                  one.push(item);
                }
                if (index == 4 || index == 5) {
                  one2.push(item);
                }
              });
              if (one.length > 3) {
                if (one2.length > 0) {
                  one.splice(4, 0, one2);
                }
              }
              console.log(one);
              this.cmsList = one;
            } else {
              this.cmsList = res.result;
            }
          } else {
            vant.Toast(res.msg);
            this.cmsList = [];
          }
        });
    },
    cmsTurn(v) {
      if (v.url && isURL(v.url)) {
        if (v.url.indexOf(window.location.origin) != -1) {
          window.open(v.url, "_self");
          location.reload();
        } else {
          window.open(v.url);
        }

      } else if (v.model && this.isJSON(v.model)) {
        var result = v.model.replace("${activityId}", v.activityId);
        this.$router.push(JSON.parse(result));
      } else {
        this.$router.push({
          name: "cmsContent",
          query: {
            id: v.id,
          },
        });
      }
    },
    // 轮播图事件监听
    bannerIndexChange(index) {
      this.bannerIndex = index;
    },
    isJSON(v) {
      try {
        JSON.parse(v);
        return true;
      } catch (error) {
        return false;
      }
    },
    download(v) {
      window.open(v);
    },
    openUrl(v) {
      if (v) {
        window.open(v);
      }
    },
    turnActivity(v) {
      if (v) {
        this.$router.replace({ name: 'cmsIndex', query: { id: v } })
        location.reload();

      }
    },
    parseMusic() {
      // var audio = document.getElementById('music1');
      if (this.onOrOff == "on") {
        this.onOrOff = "off";
        this.$refs.audio.pause();
        // audio.pause(); // 这个就是暂停
      } else {
        this.onOrOff = "on";
        this.$refs.audio.play();
        // audio.play(); //audio.play();// 这个就是播放
      }
    },

    // 微信环境检测相关方法
    checkWeixinEnvironment() {
      if (this.isWeixin) {
        // 检查是否已经显示过提示（使用 sessionStorage 避免重复提示）
        const hasShownTip = sessionStorage.getItem('weixinTipShown');
        if (!hasShownTip) {
          // 延迟显示提示，确保页面加载完成
          setTimeout(() => {
            this.showWeixinTip = true;
          }, 1000);
        }
      }
    },

    hideWeixinTip() {
      this.showWeixinTip = false;
      // 记录已显示过提示，避免重复显示
      sessionStorage.setItem('weixinTipShown', 'true');
    },

    copyCurrentUrl() {
      const currentUrl = window.location.href;

      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(currentUrl).then(() => {
          vant.Toast('链接已复制到剪贴板');
          this.hideWeixinTip();
        }).catch(() => {
          this.fallbackCopyText(currentUrl);
          this.hideWeixinTip();
        });
      } else {
        this.fallbackCopyText(currentUrl);
        this.hideWeixinTip();
      }
    },

    // 点评相关方法
    showReviewContent(platform, jumpCallback) {
      this.reviewLoading = true;
      this.showReviewDialog = true;
      this.jumpCallback = jumpCallback;
      this.currentPlatform = platform;

      // 根据平台调用不同的API
      let apiUrl = '';
      let apiParams = { activityId: this.activityId };

      switch (platform) {
        case 'douyin':
          apiUrl = '/pyp/web/activity/review/douyin';
          break;
        case 'dianping':
          apiUrl = '/pyp/web/activity/review/dianping';
          break;
        case 'meituan':
          apiUrl = '/pyp/web/activity/review/meituan';
          break;
        case 'ctrip':
          apiUrl = '/pyp/web/activity/review/ctrip';
          break;
        case 'ctrip_review':
          apiUrl = '/pyp/web/activity/review/ctrip';
          apiParams.type = 'review';
          break;
        case 'ctrip_notes':
          apiUrl = '/pyp/web/activity/review/ctrip';
          apiParams.type = 'notes';
          break;
        default:
          vant.Toast('不支持的平台');
          return;
      }

      this.$fly.get(apiUrl, apiParams).then(res => {
        this.reviewLoading = false;
        if (res.code === 200) {
          this.reviewData = res.result;
        } else {
          vant.Toast(res.msg || '获取内容失败');
          this.reviewData = {
            platform: '',
            platformCode: '',
            title: '',
            content: '',
            images: [],
            hasImages: false,
            textId: null,
            promptKeyword: ''
          };
        }
      }).catch(error => {
        this.reviewLoading = false;
        console.error('获取点评内容失败:', error);
        vant.Toast('网络请求失败');
        this.reviewData = {
          platform: '',
          platformCode: '',
          title: '',
          content: '',
          images: [],
          hasImages: false,
          textId: null,
          promptKeyword: ''
        };
      });
    },

    confirmReviewJump() {
      this.showReviewDialog = false;
      if (this.jumpCallback) {
        this.jumpCallback();
      }
    },

    copyReviewText() {
      const content = this.reviewData.content || this.reviewData.title;
      if (!content) {
        vant.Toast('暂无内容可复制');
        return;
      }

      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
          vant.Toast('已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopyText(content);
        });
      } else {
        this.fallbackCopyText(content);
      }
    },

    fallbackCopyText(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        vant.Toast('已复制到剪贴板');
      } catch (err) {
        vant.Toast('复制失败');
      }
      document.body.removeChild(textArea);
    },

    saveReviewText() {
      if (!this.reviewData.textId) {
        vant.Toast('暂无可保存的文案');
        return;
      }
      vant.Toast('文案已保存');
    },

    async saveReviewImages() {
      if (!this.reviewData.hasImages || !this.reviewData.images.length) {
        vant.Toast('暂无可保存的图片');
        return;
      }

      // 检测是否为移动端
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile) {
        // 移动端使用长按保存提示
        this.showMobileSaveGuide();
        return;
      }

      const loading = vant.Toast.loading({
        message: '正在保存图片...',
        forbidClick: true,
        duration: 0
      });

      try {
        let successCount = 0;

        for (let i = 0; i < this.reviewData.images.length; i++) {
          const image = this.reviewData.images[i];
          const imageUrl = image.mediaUrl || image.url;

          if (imageUrl) {
            try {
              await this.downloadImage(imageUrl, `${this.reviewData.platform}_图片_${i + 1}.jpg`);
              successCount++;
              // 添加延时避免浏览器阻止多个下载
              await new Promise(resolve => setTimeout(resolve, 800));
            } catch (error) {
              console.error(`下载第${i + 1}张图片失败:`, error);
              // 继续下载其他图片
            }
          }
        }

        loading.clear();
        if (successCount > 0) {
          vant.Toast.success(`成功保存 ${successCount} 张图片`);
        } else {
          vant.Toast.fail('图片保存失败，请尝试长按图片保存');
        }
      } catch (error) {
        loading.clear();
        console.error('保存图片失败:', error);
        vant.Toast.fail('保存失败，请尝试长按图片保存');
      }
    },

    // 移动端保存指引
    showMobileSaveGuide() {
      vant.Dialog.alert({
        title: '保存图片',
        message: '请长按图片选择"保存图片"或"添加到相册"来保存图片到本地',
        confirmButtonText: '我知道了'
      });
    },

    // 下载单张图片的方法
    async downloadImage(url, filename) {
      try {
        // 方案1：直接使用链接下载（适用于同域名图片）
        if (this.isSameDomain(url)) {
          const link = document.createElement('a');
          link.href = url;
          link.download = filename;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return;
        }

        // 方案2：通过fetch获取图片数据（可能有跨域问题）
        const response = await fetch(url, {
          mode: 'cors',
          credentials: 'omit'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 释放内存
        window.URL.revokeObjectURL(downloadUrl);
      } catch (error) {
        console.error('下载图片失败:', error);
        // 降级方案：直接打开图片链接
        window.open(url, '_blank');
        throw error;
      }
    },

    // 检查是否为同域名
    isSameDomain(url) {
      try {
        const urlObj = new URL(url);
        return urlObj.origin === window.location.origin;
      } catch (error) {
        return false;
      }
    },

    // 在新窗口打开所有图片
    openImagesInNewTabs() {
      if (!this.reviewData.hasImages || !this.reviewData.images.length) {
        vant.Toast('暂无图片可打开');
        return;
      }

      // 检查浏览器是否会阻止弹窗
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile) {
        // 移动端一次只打开一张图片
        vant.Dialog.confirm({
          title: '打开图片',
          message: `将依次打开 ${this.reviewData.images.length} 张图片，请在新页面中长按保存图片`
        }).then(() => {
          this.reviewData.images.forEach((image, index) => {
            const url = image.mediaUrl || image.url;
            if (url) {
              setTimeout(() => {
                window.open(url, `_blank_${index}`);
              }, index * 1000); // 每秒打开一张
            }
          });
        });
      } else {
        // PC端可以同时打开多张
        let openedCount = 0;
        this.reviewData.images.forEach((image, index) => {
          const url = image.mediaUrl || image.url;
          if (url) {
            const newWindow = window.open(url, `_blank_${index}`);
            if (newWindow) {
              openedCount++;
            }
          }
        });

        if (openedCount > 0) {
          vant.Toast.success(`已在新窗口打开 ${openedCount} 张图片，可右键保存`);
        } else {
          vant.Toast.fail('打开图片失败，可能被浏览器阻止');
        }
      }
    },

    // 复制图片链接
    copyImageUrls() {
      if (!this.reviewData.hasImages || !this.reviewData.images.length) {
        vant.Toast('暂无图片链接可复制');
        return;
      }

      const urls = this.reviewData.images.map((image, index) => {
        const url = image.mediaUrl || image.url;
        return `图片${index + 1}: ${url}`;
      }).join('\n');

      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(urls).then(() => {
          vant.Toast.success('图片链接已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopyText(urls);
        });
      } else {
        this.fallbackCopyText(urls);
      }
    },

    previewImage(index) {
      const images = this.reviewData.images.map(img => img.mediaUrl || img.url);
      vant.ImagePreview({
        images,
        startPosition: index,
        closeable: true
      });
    },

    // 重新生成点评内容
    regenerateReviewText() {
      if (!this.currentPlatform) {
        vant.Toast('无法获取当前平台信息');
        return;
      }

      this.regenerating = true;

      // 根据平台调用不同的API
      let apiUrl = '';
      let apiParams = { activityId: this.activityId };

      switch (this.currentPlatform) {
        case 'douyin':
          apiUrl = '/pyp/web/activity/review/regenerate/douyin';
          break;
        case 'dianping':
          apiUrl = '/pyp/web/activity/review/regenerate/dianping';
          break;
        case 'meituan':
          apiUrl = '/pyp/web/activity/review/regenerate/meituan';
          break;
        case 'ctrip':
          apiUrl = '/pyp/web/activity/review/regenerate/ctrip';
          break;
        case 'ctrip_review':
          apiUrl = '/pyp/web/activity/review/regenerate/ctrip';
          apiParams.type = 'review';
          break;
        case 'ctrip_notes':
          apiUrl = '/pyp/web/activity/review/regenerate/ctrip';
          apiParams.type = 'notes';
          break;
        default:
          vant.Toast('不支持的平台');
          this.regenerating = false;
          return;
      }

      // 调用异步生成接口
      this.$fly.get(apiUrl, apiParams).then(res => {
        if (res.code === 200) {
          const taskId = res.taskId;
          vant.Toast.loading({
            message: '正在重新生成文案...',
            forbidClick: true,
            duration: 0 // 不自动关闭
          });

          // 开始轮询任务状态
          this.startPollingTask(taskId);
        } else {
          this.regenerating = false;
          vant.Toast(res.msg || '生成失败');
        }
      }).catch(error => {
        this.regenerating = false;
        console.error('重新生成内容失败:', error);
        vant.Toast('网络请求失败');
      });
    },

    // 开始轮询任务状态
    startPollingTask(taskId) {
      // 清除之前的轮询
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
      }

      let pollCount = 0;
      const maxPolls = 60; // 最多轮询60次（约5分钟）

      this.pollingTimer = setInterval(() => {
        pollCount++;

        if (pollCount > maxPolls) {
          clearInterval(this.pollingTimer);
          this.regenerating = false;
          vant.Toast.clear(); // 关闭loading提示
          vant.Toast('生成超时，请稍后重试');
          return;
        }

        this.checkTaskStatus(taskId);
      }, 5000); // 每5秒轮询一次
    },

    // 检查任务状态
    checkTaskStatus(taskId) {
      this.$fly.get(`/pyp/web/activity/review/task/status/${taskId}`).then(res => {
        if (res.code === 200) {
          const taskStatus = res.result;

          if (taskStatus.status === 'completed') {
            // 任务完成，只更新文案内容，保留原有图片
            clearInterval(this.pollingTimer);
            this.regenerating = false;
            vant.Toast.clear(); // 关闭loading提示

            if (taskStatus.result) {
              const newData = taskStatus.result;
              // 只更新文案相关字段，保留原有图片
              if (newData.text) {
                this.reviewData.text = newData.text;
              }
              if (newData.title) {
                this.reviewData.title = newData.title;
              }
              if (newData.name) {
                this.reviewData.name = newData.name;
              }
              // 保留原有的 images 数组不变
              vant.Toast.success('文案已更新');
            } else {
              vant.Toast('生成失败，请重试');
            }
          } else if (taskStatus.status === 'failed') {
            // 任务失败
            clearInterval(this.pollingTimer);
            this.regenerating = false;
            vant.Toast.clear(); // 关闭loading提示
            vant.Toast(taskStatus.error || '生成失败');
          }
          // 如果是 'processing' 状态，继续轮询
        } else {
          // API调用失败，但不停止轮询，可能是临时网络问题
          console.warn('轮询任务状态失败:', res.msg);
        }
      }).catch(error => {
        console.warn('轮询请求失败:', error);
        // 网络错误不停止轮询，可能是临时问题
      });
    },

  },

  beforeDestroy() {
    // 清理轮询定时器
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
    }
  }
};
</script>

<style lang="less" scoped>
.template1 /deep/ .van-grid-item__text {
  text-align: center;
}

.template1 /deep/ .van-grid-item__content {
  background: transparent;
}

.template5 /deep/ .van-grid-item__content {
  background: transparent;
  padding: 4px 6px;
}

.template5 /deep/ .van-grid-item__icon {
  width: 100%;
}

.template5 /deep/ .van-icon__image {
  width: 100%;
  object-fit: fill;
}

.template2 /deep/ .van-grid-item__content {
  background: fade(#4b00ff, 100%);
  border-radius: 15px;
}

.template2 /deep/ .van-grid-item__text {
  color: white;
  font-size: 18px;
}

.bgStyle {
  padding-top: 20px;
  padding-bottom: 40px;
  background-size: cover;
  margin-top: -5px;
  position: relative;
}

.template3 {
  display: flex;
  flex-flow: wrap;
}

.template3 .item-big {
  width: 50%;
  height: 200px;
}

.template3 .item-big-four {
  width: 50%;
  height: 200px;
}

.template3 .item-big-four .item-child {
  display: flex;
  width: 100%;
}

.template3 .item-child-big {
  height: 190px;
  margin: 5px;
  border-radius: 8px; // background-color: #662ce5;
  background: -webkit-gradient(linear,
      left top,
      left bottom,
      from(#5f34df),
      to(#4b8ce6));
  background: -webkit-linear-gradient(top, #5f34df, #4b8ce6);
  background: linear-gradient(180deg, #5f34df, #4b8ce6);
  color: #fff;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
}

.template3 .item-child-big .font {
  margin-top: 5px;
  font-size: 20px;
  font-weight: bold;
}

.template3 .item-child-small {
  width: 50%;
  height: 90px;
  margin: 5px;
  border-radius: 8px; // background-color: #662ce5;
  background: -webkit-gradient(linear,
      left top,
      left bottom,
      from(#5f34df),
      to(#4b8ce6));
  background: -webkit-linear-gradient(top, #5f34df, #4b8ce6);
  background: linear-gradient(180deg, #5f34df, #4b8ce6);
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.template3 .item-child-small .font {
  margin-top: 5px;
  font-size: 14px;
  font-weight: bold;
}

.template6 {
  display: flex;
  flex-flow: wrap;
}

.template6 .item-three {
  width: 33.3%;
  height: 120px;
}

.template6 .item-three .item {
  height: 120px;
  margin: 0 5px;
  border-radius: 8px;
  color: #fff;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
}

.template6 .item-two {
  width: 50%;
  height: 200px;
}

.template6 .item-two .item {
  height: 200px;
  margin: 0 5px;
  border-radius: 8px;
  color: #fff;
  display: flex;
  flex-flow: column;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.template6 .item-two .item .item {
  height: 100px;
  margin: 0px;
  margin-right: 5px;
  border-radius: 8px;
  color: #fff;
  width: 100%;
}

.template6 .item-one {
  width: 100%;
  height: 50px;
}

.template6 .item-one .item {
  height: 50px;
  margin: 0 5px;
  border-radius: 8px;
  color: #fff;
  display: flex;
  flex-flow: column;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.template6 .item-two-small {
  width: 49%;
  height: 50px;
}

.template6 .item-two-small .item {
  height: 50px;
  margin: 0 5px;
  border-radius: 8px;
  color: #fff;
  display: flex;
  flex-flow: column;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.coutdown {
  .van-count-down {
    line-height: 24px;
  }

  .van-cell__title,
  .van-cell__value {
    text-align: center;
  }
}

.nav-list {
  // height: 400px;
  flex-wrap: wrap;
  margin-left: 0.3%; // justify-content: space-around;
}

.nav-list,
.nav-list .nav-item {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}

.nav-list .nav-item {
  width: 33%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  -webkit-box-pack: justify;
  justify-content: space-between;
}

.nav-list .nav-item .top {
  width: 94px;
  height: 94px; // background-color: #4B00FF;
  background: -webkit-gradient(linear,
      left top,
      left bottom,
      from(#4b8ce6),
      to(#5f34df));
  background: -webkit-linear-gradient(top, #4b8ce6, #5f34df);
  background: linear-gradient(180deg, #4b8ce6, #5f34df);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
  border-radius: 50%;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
}

.nav-list .nav-item .top .image {
  width: 64px;
  height: 64px;
}

.nav-list .nav-item .bottom {
  font-weight: 500;
  line-height: 34px;
  color: #646566;
  font-size: 14px;
}

.download {
  /deep/ .van-collapse-item__content {
    padding: 0px;
  }

  /deep/ .van-cell__value {
    color: red;
  }
}

.bottomdiy {
  margin-top: -5px;
  height: 60px;
  width: 100%;
  background-color: #254288; // background: -webkit-linear-gradient(top, #4b8ce6, #5f34df);
  // background: linear-gradient(180deg, #4b8ce6, #5f34df);
  display: flex;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  flex-direction: column;
  position: relative;

  .item {
    color: white;
  }
}

.copyright {
  height: 46px;
  font-size: 14px;
  font-weight: 400;
  line-height: 46px;
  text-align: center;
  color: black;
}

.grid-text {
  margin-top: 5px;
  color: #646566;
  font-size: 12px;
  line-height: 1.5;
  word-break: break-all;
  text-align: center;
}

.audioBase {
  width: 30px;
  height: 30px;
  position: fixed;
  top: 10px;
  right: 10px;
  background: #eee;
  z-index: 9999;
  border: 1px #eee solid;
  border-radius: 50%;
}

.audioBase #audio-btn {
  width: 30px;
  height: 30px;
  background-size: 100% 100%;
}

.audioBase .on {
  background: url("http://mpjoy.oss-cn-beijing.aliyuncs.com/20230412/a20a62a3ca5d47a08e6b1845d6b0a183.png") no-repeat 0 0;
  -webkit-animation: rotating 1.2s linear infinite;
  animation: rotating 1.2s linear infinite;
}

.audioBase .off {
  background: url("http://mpjoy.oss-cn-beijing.aliyuncs.com/20230412/2b476c2edcea42fc8c96cbf80018fc62.png") no-repeat 0 0;
}

.template62 {
  display: flex;
  flex-flow: wrap;

  .item-three {
    width: 33.3%;
    height: 100px;

    .item {
      height: 90px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-two {
    width: 50%;
    height: 220px;

    .item {
      height: 210px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);

      .item {
        height: 100px;
        margin: 0px;
        border-radius: 10px;
        color: #fff;
        width: 100%;
        box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .item-two-small {
    width: 49%;
    height: 50px;

    .item {
      height: 50px;
      margin: 0 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-two-three {
    width: 50%;
    height: 110px;

    .item {
      height: 100px;
      margin: 5px;
      border-radius: 10px;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-one {
    width: 100%;
    height: 50px;

    .item {
      height: 50px;
      margin: 0 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }
}

.template63 {
  display: flex;
  flex-flow: wrap;

  .item-three {
    width: 33.3%;
    height: 100px;

    .item {
      height: 90px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-two {
    width: 50%;
    height: 220px;

    .item {
      height: 210px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);

      .item {
        height: 100px;
        margin: 0px;
        border-radius: 10px;
        color: #fff;
        width: 100%;
        box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .item-two-small {
    width: 49%;
    height: 50px;

    .item {
      height: 50px;
      margin: 0 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-two-three {
    width: 50%;
    height: 110px;

    .item {
      height: 100px;
      margin: 5px;
      border-radius: 10px;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-one {
    width: 100%;
    height: 50px;

    .item {
      height: 50px;
      margin: 0 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }
}

.template64 {
  display: flex;
  flex-flow: wrap;

  .item-three {
    width: 33.3%;
    height: 100px;


    .item {
      position: relative;
      height: 90px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);

      .image {
        height: 50px;
        position: absolute;
        bottom: 5px;
        right: 5px;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        position: absolute;
        top: 10px;
        left: 10px;
      }
    }
  }

  .item-two {
    width: 50%;
    height: 220px;

    .item {
      .image {
        height: 80px;
        position: absolute;
        bottom: 8px;
        right: 8px;
      }

      .title {
        font-size: 24px;
        font-weight: 600;
        position: absolute;
        top: 15px;
        left: 15px;
      }

      position: relative;
      height: 210px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);

      .item {
        .image {
          height: 50px;
          position: absolute;
          bottom: 5px;
          right: 5px;
        }

        .title {
          font-size: 18px;
          font-weight: 600;
          position: absolute;
          top: 10px;
          left: 10px;
        }

        position: relative;
        height: 100px;
        margin: 0px;
        border-radius: 10px;
        color: #fff;
        width: 100%;
        box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .item-two-small {
    width: 49%;
    height: 50px;

    .item {
      .image {
        height: 50px;
        position: absolute;
        bottom: 5px;
        right: 5px;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        position: absolute;
        top: 10px;
        left: 10px;
      }

      position: relative;
      height: 50px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-two-three {
    width: 50%;
    height: 110px;

    .item {
      .image {
        height: 50px;
        position: absolute;
        bottom: 5px;
        right: 5px;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        position: absolute;
        top: 10px;
        left: 10px;
      }

      position: relative;
      height: 100px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  .item-one {
    width: 100%;
    height: 50px;

    .item {
      height: 50px;
      margin: 5px;
      border-radius: 10px;
      color: #fff;
      display: flex;
      flex-flow: column;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    }
  }
}

.ad {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}


.template7 {
  margin-left: 5%;
  margin-bottom: 10px;
  position: relative;
  height: 80px;
  width: 90%;
  border-radius: 10px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 4px;

  .image {
    width: 30px;
    height: 30px;
    margin-right: 5px;
  }
}

.task-section-card {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px;
  overflow: hidden;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.wechat-qr-container {
  text-align: center;
  padding: 20px;
  margin-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.wechat-qr-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.wechat-qr-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.wechat-qr-image:hover {
  transform: scale(1.05);
}

.wechat-qr-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.activity-header-info {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 10000;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.4);
  padding: 5px 10px;
  border-radius: 20px;
  max-width: 70%;
}

.activity-logo {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.activity-name {
  color: white;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 点评预览对话框样式 */
.review-dialog /deep/ .van-dialog {
  max-width: 90%;
  max-height: 80vh;
}

.review-dialog /deep/ .van-dialog__content {
  max-height: 60vh;
  overflow-y: auto;
}

.review-content {
  padding: 16px;
}

.review-text-section,
.review-image-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-title .van-icon {
  margin-right: 6px;
  color: #1989fa;
}

.text-content {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.text-title,
.text-body,
.text-keyword {
  margin-bottom: 8px;
  line-height: 1.5;
}

.text-title strong,
.text-body strong,
.text-keyword strong {
  color: #333;
  font-weight: 600;
}

.text-body p {
  margin: 4px 0;
  color: #666;
}

.text-actions,
.image-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.image-actions .van-button {
  flex: 1;
  min-width: 80px;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 120px;
  background: #fafafa;
  border-radius: 8px;
  margin: 16px 0;
}

.loading-state .van-loading {
  margin-bottom: 12px;
}

.loading-state .van-loading__text {
  color: #969799;
  font-size: 14px;
  font-weight: 500;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.review-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.no-content {
  text-align: center;
  padding: 20px;
}

/* 微信环境提示样式 */
.weixin-tip-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.weixin-tip-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 320px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: tipFadeIn 0.3s ease-out;
}

@keyframes tipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.tip-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-left: 8px;
}

.tip-body {
  margin-bottom: 20px;
  line-height: 1.6;
}

.tip-body p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.tip-actions {
  display: flex;
  gap: 12px;
}

.tip-actions .van-button {
  flex: 1;
}

/* 我的小店和团购券区域样式 */
.shop-group-section {
  padding: 15px;
}

.shop-group-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 我的小店卡片 */
.my-shop-card {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  color: white;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  transition: transform 0.2s ease;
}

.my-shop-card:active {
  transform: scale(0.98);
}

.shop-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.shop-icon img {
  width: 30px;
  height: 30px;
  filter: brightness(0) invert(1);
}

.shop-content {
  flex: 1;
}

.shop-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.shop-desc {
  font-size: 12px;
  opacity: 0.9;
}

.shop-arrow {
  color: rgba(255, 255, 255, 0.8);
}

/* 团购券区域 */
.group-buying-section {
  background: white;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.coupon-count {
  font-size: 12px;
  color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

/* 团购券列表 */
.coupons-container {
  margin: 0;
}

.coupon-swipe {
  margin-top: 12px;
}

.coupon-swipe .van-swipe__indicator {
  width: 6px;
  height: 6px;
  margin: 0 3px;
}

.coupon-item {
  background: white;
  border-radius: 8px;
  border: 1px solid #eee;
  padding: 12px;
  margin: 0 2px; /* 添加左右间距 */
  display: flex;
  align-items: center;
  gap: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-height: 80px; /* 确保统一高度 */
}

.coupon-item:active {
  transform: scale(0.98);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.coupon-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.coupon-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.coupon-content {
  flex: 1;
  min-width: 0;
}

.coupon-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 1;
}

.coupon-price {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b35;
}

.original-price {
  font-size: 11px;
  color: #999;
  text-decoration: line-through;
}

.coupon-platform {
  font-size: 11px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.coupon-arrow {
  color: #ccc;
  flex-shrink: 0;
}

/* 无团购券状态 */
.no-coupons {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.no-coupons-text {
  font-size: 14px;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .shop-group-section {
    margin: 10px 10px;
    padding: 12px;
  }

  .my-shop-card {
    padding: 12px;
  }

  .shop-icon {
    width: 45px;
    height: 45px;
    margin-right: 12px;
  }

  .shop-icon img {
    width: 25px;
    height: 25px;
  }

  .shop-title {
    font-size: 15px;
  }

  .coupon-swipe .van-swipe__indicator {
    width: 5px;
    height: 5px;
    margin: 0 2px;
  }

  .coupon-item {
    padding: 10px;
    gap: 10px;
    margin: 0 1px; /* 移动端减少间距 */
    min-height: 70px; /* 移动端减少高度 */
  }

  .coupon-image {
    width: 50px;
    height: 50px;
  }

  .coupon-name {
    font-size: 13px;
  }

  .current-price {
    font-size: 15px;
  }

  .coupon-platform {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 携程团购样式 */
.ctrip-group-buying-section {
  margin: 15px 0;
  padding: 0 15px;
}

.ctrip-group-buying-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.ctrip-group-buying-item:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.4);
}

.ctrip-icon {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.ctrip-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ctrip-content {
  flex: 1;
  color: white;
}

.ctrip-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.ctrip-desc {
  font-size: 13px;
  opacity: 0.9;
}

.ctrip-arrow {
  color: white;
  opacity: 0.8;
  font-size: 16px;
}
</style>