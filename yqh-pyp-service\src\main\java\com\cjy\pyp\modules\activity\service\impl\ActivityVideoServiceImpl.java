package com.cjy.pyp.modules.activity.service.impl;

import com.aliyun.vod.upload.impl.PutObjectProgressListener;
import com.aliyun.vod.upload.impl.UploadVideoImpl;
import com.aliyun.vod.upload.req.UploadStreamRequest;
import com.aliyun.vod.upload.resp.UploadStreamResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.cjy.pyp.common.exception.RRException;
import com.cjy.pyp.common.utils.DateUtils;
import com.cjy.pyp.common.utils.InitObject;
import com.cjy.pyp.modules.activity.dto.JobStatusResponse;
import com.cjy.pyp.modules.activity.dto.VideoEditRequest;
import com.cjy.pyp.modules.activity.dto.VideoEditResponse;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEditJobEntity;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.activity.service.ActivityVideoConnectService;
import com.cjy.pyp.modules.activity.service.ActivityVideoEditJobService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityTextPreGenerateService;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityVideoConnectEntity;
import com.cjy.pyp.modules.activity.utils.IceUtil;
import com.cjy.pyp.common.enume.UsageTypeEnum;
import com.alibaba.fastjson.JSON;
import com.cjy.pyp.modules.place.entity.PlaceActivityVideoEntity;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.wx.entity.WxAccount;
import com.cjy.pyp.modules.wx.service.WxAccountService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.dao.ActivityVideoDao;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;
import com.cjy.pyp.modules.activity.service.ActivityVideoService;
import org.springframework.web.multipart.MultipartFile;

@Service("activityVideoService")
public class ActivityVideoServiceImpl extends ServiceImpl<ActivityVideoDao, ActivityVideoEntity>
        implements ActivityVideoService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityVideoServiceImpl.class);

    @Autowired
    private WxAccountService wxAccountService;

    @Autowired
    private ActivityTextService activityTextService;

    @Autowired
    private IceUtil iceUtil;

    @Autowired
    private ActivityVideoEditJobService activityVideoEditJobService;

    @Autowired
    private ActivityVideoConnectService activityVideoConnectService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private ActivityImageService activityImageService;
    @Autowired
    private ActivityTextPreGenerateService activityTextPreGenerateService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String name = (String) params.get("name");
        String activityIdStr = (String) params.get("activityId");
        if (StringUtils.isEmpty(activityIdStr)) {
            throw new RuntimeException("会议不存在");
        }
        Long activityId = Long.parseLong(activityIdStr);
        String typeStr = (String) params.get("type");

        IPage<ActivityVideoEntity> page = this.page(
                new Query<ActivityVideoEntity>().getPage(params),
                new QueryWrapper<ActivityVideoEntity>()
                        .eq("activity_id", activityId)
                        .eq(StringUtils.isNotEmpty(typeStr), "type", typeStr)
                        .like(StringUtils.isNotEmpty(name), "name", name)
                        .orderBy(true, false, "id"));

        return new PageUtils(page);
    }

    @Override
    public List<ActivityVideoEntity> findByIds(List<Long> ids) {
        return ids.size() > 0 ? this.listByIds(ids) : new ArrayList<>();
    }

    @Override
    public List<ActivityVideoEntity> findByActivityIdAndType(Long activityId, Integer type) {
        return this.list(new QueryWrapper<ActivityVideoEntity>()
                .eq("activity_id", activityId)
                .eq("type", type)
                .orderByAsc("paixu"));
    }
    @Override
    public List<ActivityVideoEntity> findByActivityIdAndPlatform(Long activityId, String platform) {
        return this.list(new QueryWrapper<ActivityVideoEntity>()
                .eq("activity_id", activityId)
                .eq("platform", platform)
                .eq("type", 1)
                .orderByAsc("paixu"));
    }

    @Override
    public ActivityVideoEntity findByActivityIdAndPlatformNoUse(Long activityId, String platform) {
        return this.getOne(new QueryWrapper<ActivityVideoEntity>()
                .eq("activity_id", activityId)
                .eq("platform", platform)
                .eq("type", 1)
                .eq("use_count", 0)
                .orderByAsc("paixu")
               .last("LIMIT 1"));
    }

    @Override
    public String uploadVideoAly(MultipartFile file, Long activityId, String appid) {

        try {
            WxAccount wxAccount = wxAccountService.findByAppid(appid);
            // fileName 上传文件原始名称
            // inputStream 上传文件输入流
            InputStream inputStream = file.getInputStream();
            // title 上传之后显示的名称
            String filename = file.getOriginalFilename();// 得到上传的文件名
            String title = filename.substring(0, filename.lastIndexOf("."));
            UploadStreamRequest request = new UploadStreamRequest(wxAccount.getVodaliyunAccessKey(),
                    wxAccount.getVodaliyunAccessKeySecret(), title, filename, inputStream);
            /* 存储区域(可选) */
            // request.setStorageLocation("视频点播中视频的存储区域");
            /* 点播服务接入点 */
            request.setApiRegionId("cn-shenzhen");
            request.setPrintProgress(true);
            request.setProgressListener(new PutObjectProgressListener());
            UploadVideoImpl upload = new UploadVideoImpl();

            UploadStreamResponse response = upload.uploadStream(request);
            logger.info("RequestId=" + response.getRequestId());// 请求视频点播服务的请求ID

            String videoId = null;

            if (response.isSuccess()) {
                videoId = response.getVideoId();
                logger.info("VideoId=" + response.getVideoId());
            } else {// 如果设置回调URL无效,不影响视频上传,可以返回VideoId同时会返回错误码。其他情况上传失败时,VideoId为空,此时需要根据返回的
                throw new RRException("阿里云视频上传失败");
            }
            // 获取视频详细信息
            DefaultAcsClient client = InitObject.initVodClient(wxAccount.getVodaliyunAccessKey(),
                    wxAccount.getVodaliyunAccessKeySecret());
            GetPlayInfoRequest getVideoListRequest = new GetPlayInfoRequest();
            getVideoListRequest.setVideoId(videoId);
            GetPlayInfoResponse acsResponse = client.getAcsResponse(getVideoListRequest);
            GetPlayInfoResponse.VideoBase videoBase = acsResponse.getVideoBase();
            List<GetPlayInfoResponse.PlayInfo> playInfoList = acsResponse.getPlayInfoList();
            ActivityVideoEntity placeActivityVideoEntity = new ActivityVideoEntity();
            placeActivityVideoEntity.setFileId(videoId);
            placeActivityVideoEntity.setName(title);
            placeActivityVideoEntity.setFileSize(file.getSize());
            placeActivityVideoEntity.setDuration(StringUtils.isEmpty(videoBase.getDuration()) ? new BigDecimal(0)
                    : new BigDecimal(videoBase.getDuration()));
            placeActivityVideoEntity.setMediaUrl(playInfoList.get(0).getPlayURL());
            placeActivityVideoEntity.setFrameRate(StringUtils.isEmpty(playInfoList.get(0).getFps()) ? new BigDecimal(0)
                    : new BigDecimal(playInfoList.get(0).getFps()));
            placeActivityVideoEntity.setExpireTime(DateUtils.addDateMinutes(new Date(), 30));
            placeActivityVideoEntity.setActivityId(activityId);
            baseMapper.insert(placeActivityVideoEntity);
            return videoId;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RRException("阿里云视频上传失败");
        }
    }

    @Override
    public VideoEditResponse submitVideoEdit(VideoEditRequest request, Long userId) {
        try {
            // 1. 检查并扣减用户的allCount
            com.cjy.pyp.common.utils.R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    userId,
                    request.getActivityId(),
                    UsageTypeEnum.VIDEO_GENERATION.getCode(),
                    null,
                    "视频混剪生成");

            if (!deductResult.get("code").equals(200)) {
                throw new RRException(deductResult.get("msg").toString());
            }

            // 2. 根据activityId获取未使用的文案（useCount=0）
            ActivityTextEntity textEntity = activityTextService
                    .findByActivityIdAndAdTypeNotUse(request.getActivityId(), request.getPlatform());
            if (textEntity == null) {

                SysUserEntity sysUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
                ActivityTextEntity activityText = new ActivityTextEntity();
                activityText.setActivityId(request.getActivityId());
                activityText.setAdType(request.getPlatform());
                activityText.setModel("deepseek-chat");
                // 生成文案
                R result = activityTextService.generateText(activityText, sysUser == null ? null : sysUser.getUserId());
                if (!result.get("code").equals(200)) {
                    throw new RRException("生成文案失败");
                }
                textEntity = (ActivityTextEntity) result.get("activityText");
                // 异步预生成2条文案
                activityTextPreGenerateService.preGenerateTextAsync(activityText,
                        sysUser == null ? null : sysUser.getUserId());
            }

            // 根据activityId获取素材视频（type=0）
            List<ActivityVideoEntity> videos = this.findByActivityIdAndType(request.getActivityId(), 0);
            if (videos.isEmpty()) {
                throw new RRException("该活动下没有可用的素材视频（type=0）");
            }

            // 如果视频数量超过3个，随机选择3个
            List<ActivityVideoEntity> selectedVideos = videos;
            if (videos.size() > 3) {
                selectedVideos = new ArrayList<>(videos);
                Collections.shuffle(selectedVideos);
                selectedVideos = selectedVideos.subList(0, 3);
                logger.info("视频数量超过3个，随机选择了3个视频进行处理");
            }

            // 构建视频URL列表和视频ID列表
            List<String> videoUrls = new ArrayList<>();
            List<Long> videoIds = new ArrayList<>();
            for (ActivityVideoEntity video : selectedVideos) {
                String mediaUrl = video.getMediaUrl();
                if (mediaUrl == null || mediaUrl.trim().isEmpty()) {
                    logger.error("素材视频URL为空: videoId=" + video.getId() + ", name=" + video.getName());
                    throw new RRException("素材视频URL为空，视频ID: " + video.getId());
                }
                logger.info("添加素材视频: videoId=" + video.getId() + ", name=" + video.getName() + ", url=" + mediaUrl);
                videoUrls.add(mediaUrl);
                videoIds.add(video.getId());
            }

            // 设置标题、字幕和口播文本（使用文案的name作为标题，result作为字幕和口播）
            String title = textEntity.getName();
            String subtitle = textEntity.getResult();
            String voiceText = textEntity.getResult();

            // 提交混剪任务
            Map<String, String> jobResult = iceUtil.submitVideoEditJob(videoUrls, title, subtitle, voiceText);
            String jobId = jobResult.get("jobId");
            String expectedOutputUrl = jobResult.get("expectedOutputUrl");

            // 保存任务信息到数据库
            ActivityVideoEditJobEntity jobEntity = new ActivityVideoEditJobEntity();
            jobEntity.setJobId(jobId);
            jobEntity.setActivityId(request.getActivityId());
            jobEntity.setTextId(textEntity.getId());
            jobEntity.setVideoIds(JSON.toJSONString(videoIds));
            jobEntity.setTitle(title);
            jobEntity.setSubtitle(subtitle);
            jobEntity.setVoiceText(voiceText);
            jobEntity.setPlatform(request.getPlatform());
            jobEntity.setVoiceType(request.getVoiceType());
            jobEntity.setStatus("Init");
            jobEntity.setOutputUrl(expectedOutputUrl); // 提前记录预期的输出URL
            jobEntity.setStartTime(new Date());
            activityVideoEditJobService.save(jobEntity);
            // 次数+1
            activityTextService.incrementUseCount(textEntity.getId());
            // 构建响应
            VideoEditResponse response = new VideoEditResponse();
            response.setJobId(jobId);
            response.setStatus("Init");
            response.setCreateTime(new Date());

            return response;

        } catch (Exception e) {
            logger.error("提交视频混剪任务失败", e);
            throw new RRException("提交视频混剪任务失败: " + e.getMessage());
        }
    }

    @Override
    public JobStatusResponse getJobStatus(String jobId, String appid) {
        try {
            // 从数据库获取任务信息
            ActivityVideoEditJobEntity jobEntity = activityVideoEditJobService.findByJobId(jobId);
            if (jobEntity == null) {
                throw new RRException("任务不存在");
            }

            // 查询阿里云任务状态
            Map<String, Object> statusInfo = iceUtil.getJobStatus(jobId);
            String currentStatus = (String) statusInfo.get("status");

            // 如果状态有变化，更新数据库
            if (!currentStatus.equals(jobEntity.getStatus())) {
                activityVideoEditJobService.updateJobStatus(jobId, currentStatus, null);
                jobEntity.setStatus(currentStatus);

                // 如果任务成功完成，保存成品视频并更新关联关系（传入已获取的状态信息，避免重复调用）
                if ("Success".equals(currentStatus)) {
                    handleSuccessfulVideoEdit(jobEntity, appid, statusInfo);
                }
            }

            JobStatusResponse response = new JobStatusResponse();
            response.setJobId(jobId);
            response.setJobType("VIDEO_EDIT");
            response.setStatus(jobEntity.getStatus());
            response.setOutputUrl(jobEntity.getOutputUrl());
            response.setCreateTime(jobEntity.getCreateOn());
            response.setCompleteTime(jobEntity.getCompleteTime());
            response.setProgress(jobEntity.getProgress());
            response.setErrorMessage(jobEntity.getErrorMessage());

            return response;

        } catch (Exception e) {
            logger.error("查询任务状态失败", e);
            throw new RRException("查询任务状态失败: " + e.getMessage());
        }
    }

    @Override
    public String generateSubtitle(Long videoId, String appid) {
        try {
            ActivityVideoEntity video = this.getById(videoId);
            if (video == null) {
                throw new RRException("视频不存在");
            }

            return iceUtil.generateSubtitle(video.getMediaUrl());

        } catch (Exception e) {
            logger.error("生成字幕失败", e);
            throw new RRException("生成字幕失败: " + e.getMessage());
        }
    }

    @Override
    public String synthesizeVoice(String text, String voice, String appid) {
        try {
            return iceUtil.synthesizeVoice(text, voice);

        } catch (Exception e) {
            logger.error("语音合成失败", e);
            throw new RRException("语音合成失败: " + e.getMessage());
        }
    }

    @Override
    public List<JobStatusResponse> getEditJobsByActivity(Long activityId, String appid) {
        try {
            // 查询活动下的所有混剪任务
            List<ActivityVideoEditJobEntity> jobEntities = activityVideoEditJobService.list(
                    new QueryWrapper<ActivityVideoEditJobEntity>()
                            .eq("activity_id", activityId)
                            .orderByDesc("create_on"));

            List<JobStatusResponse> responses = new ArrayList<>();
            for (ActivityVideoEditJobEntity jobEntity : jobEntities) {
                JobStatusResponse response = new JobStatusResponse();
                response.setJobId(jobEntity.getJobId());
                response.setJobType("VIDEO_EDIT");
                response.setStatus(jobEntity.getStatus());
                response.setOutputUrl(jobEntity.getOutputUrl());
                response.setCreateTime(jobEntity.getCreateOn());
                response.setCompleteTime(jobEntity.getCompleteTime());
                response.setProgress(jobEntity.getProgress());
                response.setErrorMessage(jobEntity.getErrorMessage());

                // 添加任务详细信息
                StringBuilder details = new StringBuilder();
                details.append("标题: ").append(jobEntity.getTitle()).append("; ");
                details.append("文案ID: ").append(jobEntity.getTextId()).append("; ");
                details.append("视频数量: ").append(JSON.parseArray(jobEntity.getVideoIds()).size());
                response.setDetails(details.toString());

                responses.add(response);
            }

            return responses;

        } catch (Exception e) {
            logger.error("获取活动混剪任务失败", e);
            throw new RRException("获取活动混剪任务失败: " + e.getMessage());
        }
    }

    /**
     * 处理混剪任务成功完成的后续操作
     *
     * @param jobEntity 任务实体
     * @param appid     应用ID
     */
    private void handleSuccessfulVideoEdit(ActivityVideoEditJobEntity jobEntity, String appid) {
        this.handleSuccessfulVideoEdit(jobEntity, appid, null);
    }

    /**
     * 处理混剪任务成功完成的后续操作（重载方法，支持传入已获取的状态信息）
     *
     * @param jobEntity  任务实体
     * @param appid      应用ID
     * @param statusInfo 已获取的状态信息（可选，避免重复调用）
     */
    private void handleSuccessfulVideoEdit(ActivityVideoEditJobEntity jobEntity, String appid,
            Map<String, Object> statusInfo) {
        try {
            logger.info("开始处理成功完成的混剪任务: jobId={}", jobEntity.getJobId());

            // 1. 获取任务状态信息，包含MediaId（如果没有传入则重新获取）
            if (statusInfo == null) {
                statusInfo = iceUtil.getJobStatus(jobEntity.getJobId());
            }
            String mediaId = (String) statusInfo.get("mediaId");

            // 2. 如果有MediaId，尝试获取视频元数据信息（只在这里获取一次）
            Map<String, Object> videoMetadata = new HashMap<>();
            if (mediaId != null) {
                try {
                    videoMetadata = iceUtil.getMediaInfoWithMetadata(mediaId);
                    logger.info("获取到视频元数据: {}", videoMetadata);
                } catch (Exception e) {
                    logger.warn("获取视频元数据失败，使用默认值: {}", e.getMessage());
                }
            }

            // 3. 创建成品视频记录
            ActivityVideoEntity resultVideo = new ActivityVideoEntity();
            resultVideo.setFileId(jobEntity.getJobId()); // 使用jobId作为fileId
            resultVideo.setName(jobEntity.getTitle());
            resultVideo.setPlatform(jobEntity.getPlatform());
            resultVideo.setActivityId(jobEntity.getActivityId());
            resultVideo.setType(1); // 1表示成品
            resultVideo.setActivityTextId(jobEntity.getTextId()); // 标记使用的文案ID
            resultVideo.setExpireTime(DateUtils.addDateMinutes(new Date(), 30));

            // 4. 设置从阿里云获取的视频元数据信息
            if (videoMetadata.containsKey("fileUrl")) {
                resultVideo.setMediaUrl((String) videoMetadata.get("fileUrl"));
            }
            if (videoMetadata.containsKey("fileSize")) {
                resultVideo.setFileSize(Long.parseLong(videoMetadata.get("fileSize").toString()));
            }
            if (videoMetadata.containsKey("duration")) {
                Object duration = videoMetadata.get("duration");
                if (duration instanceof Number) {
                    resultVideo.setDuration(new BigDecimal(duration.toString()));
                }
            }
            if (videoMetadata.containsKey("fps")) {
                Object fps = videoMetadata.get("fps");
                if (fps instanceof Number) {
                    resultVideo.setFrameRate(new BigDecimal(fps.toString()));
                }
            }

            // 5. 如果没有从API获取到元数据，使用计算的默认值
            if (resultVideo.getDuration() == null) {
                // 根据文案长度估算时长（作为兜底方案）
                String voiceText = jobEntity.getVoiceText();
                if (voiceText != null && !voiceText.trim().isEmpty()) {
                    double estimatedDuration = calculateTextDuration(voiceText);
                    resultVideo.setDuration(new BigDecimal(estimatedDuration));
                    logger.info("使用估算时长: {}秒", estimatedDuration);
                }
            }

            this.save(resultVideo);
            logger.info("成品视频记录已保存: id={}, fileId={}, duration={}秒",
                    resultVideo.getId(), resultVideo.getFileId(), resultVideo.getDuration());

            // 2. 更新文案使用次数
            activityTextService.incrementUseCount(jobEntity.getTextId());

            // 3. 保存素材视频和成品视频的关联关系
            List<Long> sourceVideoIds = JSON.parseArray(jobEntity.getVideoIds(), Long.class);
            activityVideoConnectService.saveVideoConnections(
                    jobEntity.getActivityId(),
                    sourceVideoIds,
                    resultVideo.getId());
            this.incrementUseCount(sourceVideoIds);

            // 4. 更新任务记录中的输出文件ID和元数据信息
            jobEntity.setOutputFileId(resultVideo.getFileId());

            // 将获取到的元数据也保存到任务记录中
            if (videoMetadata.containsKey("fileUrl")) {
                jobEntity.setOutputUrl((String) videoMetadata.get("fileUrl"));
            }
            if (videoMetadata.containsKey("fileSize")) {
                jobEntity.setFileSize(Long.parseLong(videoMetadata.get("fileSize").toString()));
            }
            if (videoMetadata.containsKey("duration")) {
                Object duration = videoMetadata.get("duration");
                if (duration instanceof Number) {
                    jobEntity.setDuration(((Number) duration).intValue());
                }
            } else if (resultVideo.getDuration() != null) {
                // 如果没有从API获取到，使用计算的时长
                jobEntity.setDuration(resultVideo.getDuration().intValue());
            }

            activityVideoEditJobService.updateById(jobEntity);

            // log.error("混剪任务成功处理完成，任务ID: {}, 成品视频ID: {}",
            // jobEntity.getJobId(), resultVideo.getId());

        } catch (Exception e) {
            logger.error("处理混剪成功后续操作失败", e);
            // 这里不抛出异常，避免影响状态查询的正常返回
        }
    }

    @Override
    public void handleSuccessfulVideoEditForScheduler(String jobId) {
        handleSuccessfulVideoEditForScheduler(jobId, null);
    }

    @Override
    public void handleSuccessfulVideoEditForScheduler(String jobId, Map<String, Object> statusInfo) {
        try {
            logger.info("定时器处理成功完成的混剪任务: jobId=" + jobId);

            // 获取任务信息
            ActivityVideoEditJobEntity jobEntity = activityVideoEditJobService.findByJobId(jobId);
            if (jobEntity == null) {
                logger.error("无法找到任务记录: jobId=" + jobId);
                return;
            }

            // 调用私有方法处理成功任务，使用"scheduler"作为appid，传入已获取的状态信息
            handleSuccessfulVideoEdit(jobEntity, "scheduler", statusInfo);

            logger.info("定时器处理混剪任务完成: jobId=" + jobId);

        } catch (Exception e) {
            logger.error("定时器处理成功混剪任务失败: jobId=" + jobId, e);
            throw new RRException("定时器处理成功混剪任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据文案长度估算视频时长
     * 
     * @param text 文案内容
     * @return 估算的时长（秒）
     */
    private double calculateTextDuration(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 3.0; // 默认3秒
        }

        // 去除标点符号和空格，只计算有效字符
        String cleanText = text.replaceAll("[\\p{Punct}\\s]", "");
        int charCount = cleanText.length();

        // 根据字符数量估算时长
        // 假设每秒可以播放3-4个汉字，这里使用3.5个字符/秒的速度
        double estimatedDuration = charCount / 3.5;

        // 设置最小和最大时长限制
        double minDuration = 3.0; // 最少3秒
        double maxDuration = 60.0; // 最多60秒

        estimatedDuration = Math.max(minDuration, Math.min(maxDuration, estimatedDuration));

        logger.info("文案字符数: {}, 估算时长: {}秒", charCount, estimatedDuration);
        return estimatedDuration;
    }

    @Override
    public void incrementUseCount(List<Long> videoIds) {
        List<ActivityVideoEntity> activityVideoEntities = this.findByIds(videoIds);
        if (CollectionUtils.isEmpty(activityVideoEntities)) {
            return;
        }
        activityVideoEntities.forEach(e -> {
            e.setUseCount(e.getUseCount() == null ? 1 : e.getUseCount() + 1);
        });
        this.updateBatchById(activityVideoEntities);
    }

    @Override
    public ActivityVideoEntity generateFinishedImages(Long activityId, String platform, String mediaType,
            Integer imageCount) throws Exception {

        if (activityId == null) {
            throw new RRException("活动ID不能为空");
        }

        if (!"image".equals(mediaType)) {
            throw new RRException("媒体类型必须为image");
        }

        // 1. 查找对应平台的文案
        ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeNotUse(activityId, platform);

        if (textEntity == null) {
            SysUserEntity sysUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            ActivityTextEntity activityText = new ActivityTextEntity();
            activityText.setActivityId(activityId);
            activityText.setAdType(platform);
            activityText.setModel("deepseek-chat");
            // 生成文案
            R result = activityTextService.generateText(activityText, sysUser == null ? null : sysUser.getUserId());
            if (!result.get("code").equals(200)) {
                throw new RRException("生成文案失败");
            }
            textEntity = (ActivityTextEntity) result.get("activityText");
            // 异步预生成2条文案
            activityTextPreGenerateService.preGenerateTextAsync(activityText,
                    sysUser == null ? null : sysUser.getUserId());
        }

        // 3. 查找可用的图片素材 - 使用按平台的方法
        List<ActivityImageEntity> availableImages = activityImageService.findByActivityIdNoUseLimitByPlatform(activityId,
                platform, imageCount);
        if (availableImages == null || availableImages.isEmpty()) {
            throw new RRException("没有可用的图片素材");
        }

        ActivityVideoEntity product = new ActivityVideoEntity();

        long totalSize = availableImages.stream()
                .mapToLong(ActivityImageEntity::getFileSize)
                .sum();
        product.setFileSize(totalSize);
        product.setActivityId(activityId);
        product.setActivityTextId(textEntity.getId());
        product.setName(textEntity.getName());
        product.setPlatform(platform);
        product.setMediaType("image");
        product.setType(1); // 1表示成品
        product.setImageCount(imageCount != null ? imageCount : 3);

        // 保存成品记录
        this.save(product);
        List<ActivityVideoConnectEntity> activityVideoConnectEntities = availableImages.stream().map(e -> {

            ActivityVideoConnectEntity connect = new ActivityVideoConnectEntity();
            connect.setConnectActivityVideoId(product.getId());
            connect.setActivityImageId(e.getId());
            connect.setActivityId(activityId);
            connect.setPaixu(0);
            return connect;
        }).collect(Collectors.toList());
        activityVideoConnectService.saveBatch(activityVideoConnectEntities);
        // 文案使用次数+1
        activityTextService.incrementUseCount(textEntity.getId());

        // 图片按平台使用次数+1
        for (ActivityImageEntity image : availableImages) {
            activityImageService.incrementUseCountByPlatform(image.getId(), platform, activityId);
        }

        return product;
    }

    @Override
    public void incrementUseCount(Long textId) {
        ActivityVideoEntity entity = this.getById(textId);
        if (entity != null) {
            entity.setUseCount(entity.getUseCount() == null ? 1 : entity.getUseCount() + 1);
            this.updateById(entity);
        }
    }
}